<template>
  <div class="">
    <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
      <div class="vocationPull" style="flex: 1; height: 56px">
        <div class="switch">
          <div class="AIlogo"></div>
          <a-switch
            checked-children="on"
            un-checked-children="off"
            v-model:checked="switchOnOff"
          />
        </div>
        <a-config-provider
          :locale="zhCN"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        >
          <div class="line"></div>

          <div class="lines"></div>
          <a-input
            v-model:value="name"
            class="inputClass"
            allow-clear
            height="56px"
            @keyup.enter="seekContent"
            placeholder="请输入案例名称、标签等关键词进行检索"
          />
          <div
            class="seekInfo"
            :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
            @click="seekContent"
          >
            <img src="@/assets/images/home/<USER>" alt="" />
            <div>搜索</div>
          </div>
        </a-config-provider>
      </div>
    </div>
    <div class="newLoading" v-if="AIsearch">
      <loadingSmall />
    </div>
    
    <div class="selectData" ref="selectData" v-if="true || !switchOnOff">
      <!-- <div :class="['selcet_box',{ showMore: showScense == 'label' && showIndex === index },]" v-for="(val, index) in vocationList" :key="index">
        <div class="left_select">{{ val.label }}：</div>
        <div :class="['right_select',{ showHidden: showScense == 'label' && showIndex === index },]">
          <span ref="box" v-if="val.label == '行业'" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: activeKey === value.value }" style="height: 49px;">
            <div class="title" @click="providerBtn(value, 'default', index)" @mouseenter="providerEnter(value,index)">
              {{ value.label }}
            </div>
          </span>
        </div>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex !== index"
          @click="showMore('label', index)">更多<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex === index"
          @click="showless('label_less', index)">收起<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
      </div> -->

      <div :class="[
        'selcet_box',
        { showMore: showScense == 'label' && showIndex === index },
      ]" v-for="(val, index) in vocationList" :key="index">
        <div class="left_select" :style="{
          'background-color': val.level === 2 ? '#F3F8FF' : (val.level === 3 ? '#DDEAFF' : '#FFFFFFFF'),
        }">{{ val.label }}</div>
        <div :class="[
          'right_select',
          { showHidden: showScense == 'label' && showIndex === index },
        ]" :style="{
          'background-color': val.level === 2 ? '#F3F8FF' : (val.level === 3 ? '#DDEAFF' : '#FFFFFFFF'),
        }">
          <span v-if="val.label == '行业'" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: providerSelect.includes(value.label) }" style="height: 49px">
            <div class="title" @click="providerNewBtn(value, 'default', index)" :style="{
              'background-color': providerSelect.includes(value.label) ? '#F3F8FF' : '#FFFFFFFF',
            }">
              {{ value.label }}
            </div>
          </span>
          <span v-else-if="val.label == '区域'" v-for="(value, key4) in val.children" :key="key4"
            :class="{ activeBtn: citySelectList.includes(value.label) }" style="height: 49px">
            <div class="title" @click="providerNewBtn(value, 'city', index)" :style="{
              'background-color': citySelectList.includes(value.label) ? '#F3F8FF' : '#FFFFFFFF',
            }">
              {{ value.label }}
            </div>
          </span>
          <span v-else-if="val.label == '提供方'" v-for="(value, key3) in val.children" :key="key3"
            :class="{ activeBtn: providerName === value.label }" style="height: 49px">
            <div class="title" @click="providerClick(value, index)">
              {{ value.label }}
            </div>
          </span>
          <span v-else v-for="(value, key2) in val.children" :key="key2"
            :class="{ activeBtn: selectList.indexOf(value.label) > -1 }" :style="{
              height: showLast && showId == value.value ? '89px' : '49px',
            }">
            <div v-if="val.level !== 2 && val.level !== 3" class="title" @click="labelSelect(value, 'default', index)">
              {{ value.label }}
            </div>
            <div class="last_data" v-if="val.level !== 2 && val.level !== 3 && showLast && showId == value.value">
              <span v-for="(e, i) in value.children" @click="labelSelect(e, 'last')"
                :class="{ activeBtn: selectList.indexOf(e.label) > -1 }" :key="i" style="width: 60px">
                {{ e.label }}
              </span>
            </div>
          </span>
          <span ref="box" v-if="val.level === 2" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: val.id ? citySelectList.includes(value.label) : providerSelect.includes(value.label) }" style="height: 49px;" :style="{
              'background-color': val.id ? citySelectList.includes(value.label) ? '#DDEAFF' : '#F3F8FF' : providerSelect.includes(value.label) ? '#DDEAFF' : '#F3F8FF',
            }">
            <div class="title" @click="providerNewNewBtn(value, val.id ? 'city' :'default', index)">
              <!--@mouseenter="providerEnter(value, index)"-->
              {{ value.label }}
            </div>
          </span>
          <span ref="box" v-if="val.level === 3" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: val.id ? citySelectList.includes(value.label) : providerSelect.includes(value.label) }" :style="{
              'height': '49px',
              'background-color': val.id ? citySelectList.includes(value.label) ? '#DDEAFF' : '#DDEAFF' : providerSelect.includes(value.label) ? '#DDEAFF' : 'F3F8FF'
            }">
            <div class="title" @click="providerNewNewNewBtn(value, val.id ? 'city' :'default', index)">
              {{ value.label }}
            </div>
          </span>
        </div>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex !== index"
          @click="showMore('label', index)">更多<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex === index"
          @click="showless('label_less', index)">收起<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
      </div>

      <div v-if="vocationList[0]" v-for="(value, key1) in vocationList[0].children" @mouseleave="providerLeave(value,key1)">
      	<div class="last_data_top"  :style="getBoxTitle(key1)" v-if="showLast && showId == value.value" @click="providerBtn(value, 'default', key1)">{{ value.label }}</div>
      	<div class="last_data" v-if="showLast && showId == value.value" :style="getBoxLeft(key1)">
          <!--{ left: -135 * key1 + 'px' }-->
          <span v-for="(e, i) in value.children" @click="providerBtn(e, 'last', i , value)" style="width: auto;padding: 11px 20px;cursor: pointer;"
            :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }" :key="i">
            {{ e.label }}
          </span>
       </div>
      </div>
      
      <div v-if="!showLabel" class="second_line">
        <span @click="toggleShowLabel">收起</span>
        <div class="img_box">
          <img src="@/assets/images/solution/home/<USER>" class="first" alt="" />
          <img src="@/assets/images/solution/home/<USER>" class="sec" alt="" />
        </div>
      </div>
      <div class="select_boot flex">
        <div>
          已选条件
          <span v-if="providerSelect.length > 0">行业：</span>
          <span v-for="(val, index) in providerSelect" :key="index">
            <span style="margin-left: 8px">
              {{ val }}
            </span>
            <img src="@/assets/images/solution/home/<USER>" alt=""
              style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(val, index, 'pro')" />
          </span>
          <span class="label" v-if="citySelectList.length > 0">区域：</span>
          <span v-for="(item, index) in citySelectList" :key="key">
            <span style="margin-left: 8px">
              {{ item }}
            </span>
            <img src="@/assets/images/solution/home/<USER>" alt=""
              style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(item, index)" />
          </span>
        </div>
        <div class="right_con">
          共找到 <span>{{ totalItemCount }}</span> 条结果
        </div>
      </div>
    </div>
    
    <div class="tabContent">
      <div v-if="tableList && tableList.length > 0" style="width: 100%">
        <div class="AITips flex align-center" v-if="!showPagination">
          <img
            style="width: 40px; height: 40px; margin-right: 10px"
            src="../../../../../assets/images/AI/ai.png"
            alt=""
          />
          <div class="words">以下是AI助手为您找到的相关结果</div>
        </div>
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in tableList" :key="index">
              <div
                :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                  },
                ]"
                @mouseenter="contentColor(index)"
                @mouseleave="contentLeave"
                @click="proDetail(item)"
              >
                <div style="display: flex; margin: 24px">
                  <div>
                    <img
                      v-if="item.image"
                      v-lazy="`${item.image}`"
                      style="width: 168px; height: 105px"
                    />
                    <div
                      v-else
                      style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      "
                      :style="backgroundStyles()"
                    >
                      <p
                        style="
                          font-size: 7px;
                          font-weight: 700;
                          display: block;
                          color: #0a7aee;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                        "
                      >
                        {{ item.caseName || item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <div class="card_title">
                          {{ item.caseName || item.name }}
                        </div>
                      </div>
                    </div>
                    <div class="card_des">
                      {{ item.constructionContent }}
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          ">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{
                            item.viewCount }}</span>
                          <span v-else>-</span>
                        </div>
                        <div style="display: flex; align-items: center">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                            item.downloadCount }}</span>
                          <span v-else>-</span>
                        </div>
                      </div>
                      <div>
                        <button
                          class="cart-button"
                          disabled
                          v-if="item.addCart"
                        >
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span
                          >
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage">
          <a-pagination
            v-model:pageSize="pageItemSize"
            v-model:current="currentPage"
            :pageSizeOptions="pageSizeOptions"
            show-quick-jumper
            show-size-changer
            :total="totalItemCount"
            @change="pageChange"
            @showSizeChange="sizeChange"
            class="mypage"
          />
        </div>
      </div>
      <div v-if="tableList.length == 0" class="emptyPhoto">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
      <div class="loading" v-show="loadingShow">
        <a-spin />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { getTradeList, getLabelTreeList } from "@/api/solutionNew/home";
import { useHomeStore } from "@/store";
import bac from "@/assets/images/noDataBac.png";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { getSceneSchemeList, getNewCaseList } from "@/api/moduleList/home";
import { AISearch, AIvoice } from "@/api/AI/ai.js";
import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";
import { getCountryAddressData } from "@/utils/areaList.js"

export default defineComponent({
  components: {
    voiceRecorder,
    loadingSmall,
  },
  props: {
    sourceType: {
      type: String,
      default: "1",
    },
    reGetList: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const data = reactive({
      name: "",
      moment: "",
      backgroundImage: bac,
      sourceType: props.sourceType,
      loadingShow: true,
      showLabel: true,
      AIsearch: false,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tabList: [],
      vocationList: [],
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      labelIdlist: [],
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      selectListOld: [],
      showMore: false,
      showScense: "",
      morePro: true,
      providerSelect: [],
      showIndex: "",
      showPagination: true,
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      AISearchType: 7,
      tableAIAllList: [],
      filterArr:[],
      newContentName: "手动定制",
      cityAreaList:getCountryAddressData('city'),
      citySelectList:[],
      citySelectListOld:[],
    });
    watch(
      () => props.reGetList,
      (val) => {
        if (val) {
          data.activeKey = "";
          data.showLast = false;
          data.showId = "";
          data.name = "";
          data.labelIdlist = [];
          data.providerSelect = [];
          data.selectList = [];
          data.selectListNew = [];
          data.selectListOld = [];
          data.currentPage = "1";
          data.pageItemSize = "10";
          getList();
        }
      }
    );

    watch(
      () => props.sourceType,
      (val) => {
        if (val == "1") {
          data.AISearchType = 2;
        }
        if (val == "2") {
          data.AISearchType = 7;
        }
        data.sourceType = val;
        data.activeKey = "";
        data.showLast = false;
        data.showId = "";
        data.name = "";
        data.labelIdlist = [];
        data.providerSelect = [];
        data.selectList = [];
        data.selectListNew = [];
        data.selectListOld = [];
        data.currentPage = "1";
        data.pageItemSize = "10";
        getList();
      }
    );

    const aiFilter = () => {
      let industryId = data.selectListOld.length == 2
              ? data.selectListOld[1]
              : data.selectListOld.length == 1
              ? data.selectListOld[0]
              : ""
      let province = ''
      let city = ''
      // let county = ''
      if(data.citySelectList.length > 0){
          if(["北京市","上海市","重庆市","天津市"].includes(data.citySelectList[0])){
            province = ''
            city = data.citySelectList[0] ? data.citySelectList[0] : ''
            // county = data.citySelectList[1] ? data.citySelectList[1] : null
          }else{
            province = data.citySelectList[0]
            city = data.citySelectList[1] ? data.citySelectList[1] : ''
            // county = data.citySelectList[2] ? data.citySelectList[2] : null
          }
        }
      if(industryId != ''){
        data.filterArr = data.tableAIAllList.filter(item=>{
          return item.industryIdReq.includes(industryId)
        })
      }
      if(province != ''){
        data.filterArr = data.tableAIAllList.filter(item=>{
          return item.province == province
        })
      }
      if(city != ''){
        data.filterArr = data.tableAIAllList.filter(item=>{
          return item.city == city
        })
      }
      if(industryId != '' && province != '' && city == ''){
        let arr = data.tableAIAllList.filter(item=>{
          return item.industryIdReq.includes(industryId)
        })
        data.filterArr = arr.filter(item=>{
          return item.province == province
        })
      }
      if(industryId != '' && province != '' && city != ''){
        let arr = data.tableAIAllList.filter(item=>{
          return item.industryIdReq.includes(industryId)
        })
        let arrP = arr.filter(item=>{
          return item.province == province
        })
        data.filterArr = arrP.filter(item=>{
          return item.city == city
        })
      }
      if(industryId == '' && data.citySelectList.length == 0){
        data.filterArr = data.tableAIAllList
      }
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      data.totalItemCount = data.filterArr.length
    }

    const getList = () => {
      if (data.sourceType === "2") {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          industryId:
            data.selectListOld.length == 2
              ? data.selectListOld[1]
              : data.selectListOld.length == 1
              ? data.selectListOld[0]
              : "",
          labelIds: data.labelIdlist,
          type: 8,
        };
        data.loadingShow = true;
        getSceneSchemeList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
            	item.image = item.image.split(",")[0];
              item.description = item.summary;
              item.logo = item.image;
            });

            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch(() => {
            data.loadingShow = false;
          });
      } else {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          shelfStatus : 1,
          type: 8,
          caseType:1,
          industryId:
            data.selectListOld.length == 2
              ? data.selectListOld[1]
              : data.selectListOld.length == 1
              ? data.selectListOld[0]
              : "",
        };
        if(data.citySelectList.length > 0){
          if(["北京市","上海市","重庆市","天津市"].includes(data.citySelectList[0])){
            pageParams.province = null
            pageParams.city = data.citySelectList[0] ? data.citySelectList[0] : null
            pageParams.county = data.citySelectList[1] ? data.citySelectList[1] : null
          }else{
            pageParams.province = data.citySelectList[0]
            pageParams.city = data.citySelectList[1] ? data.citySelectList[1] : null
            pageParams.county = data.citySelectList[2] ? data.citySelectList[2] : null
          }
        }
        data.loadingShow = true;
        getNewCaseList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.forEach((item) => {
              item.image = item.imageUrl.split(",")[0];
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch(() => {
            data.loadingShow = false;
          });
      }
    };
    getList();
    const providerList = () => {
      getLabelTreeList().then((res) => {
        data.vocationList = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
              label: child.name,
              value: child.id,
              level: 2,
              children: child.children
                ? child.children.map((ele) => ({
                  label: ele.name,
                  value: ele.id,
                  level: 3,
                  children: ele.children
                    ? ele.children.map((three) => ({
                      label: three.name,
                      value: three.id,
                      level: 4,
                    }))
                    : undefined,
                }))
                : undefined,
            }))
            : undefined,
        }));
        data.vocationList = data.vocationList.filter(item => item.label == '行业');
        let cityList = ['北京市','上海市','广东省','深圳市','江苏省','浙江省','安徽省'].reverse()
        cityList.forEach(item=>{
          const c = data.cityAreaList.find(item1 => item1.label === item); // 找到 id 为 'c' 的对象
          data.cityAreaList.sort((a, b) => {
            if (a === c) return -1; // 把 c 排到前面
            if (b === c) return 1;
            return 0;
          });
        })
        data.vocationList.push({
          label:"区域",
          value:'',
          length:data.cityAreaList.length,
          children:data.cityAreaList
        })
        //      data.vocationList = data.vocationList.slice(1);
      });
    };
    providerList();
    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };

    const add = (id) => {
      let addParams = {
        schemeId: id,
        type: "1",
      };
      if (data.sourceType == "1") {
        addParams.type = "1";
      } else {
        addParams.type = "3";
      }
      addShoppingCart(addParams).then(() => {
        if (data.showPagination) {
          getList();
        } else {
          getAIList();
        }
        eventBus.emit("cartRefresh");
      });
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = vocation.value = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      if (data.sourceType == "1") {
        router.push({
          query: {
            id: val.id,
          },
          name: "caseDetailNew",
        });
      } else {
        router.push({
          query: {
            id: val.id,
            activeBtn: 2,
          },
          name: "caseDetailNew",
        });
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
      data.tableList.forEach((item) => {
        item.image = item.imageUrl.split(",")[0];
      });
      data.totalItemCount = data.tableAIAllList.length
    };
    const pageChange = (page, _pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (_current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    const labelSelect = (value, index, type = "default") => {
      if (value.children && type !== "last") {
        if (data.selectList.includes(value.label)) {
          data.showLast = false;
        } else {
          data.showLast = true;
        }
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      }
      if (data.selectList.includes(value.label)) {
        const index = data.selectList.findIndex((item) => item === value.label);
        if (index !== -1) {
          data.selectList.splice(index, 1);
        }
        const index1 = data.selectListNew.findIndex(
          (item) => item === value.value
        );
        if (index1 !== -1) {
          data.selectListNew.splice(index, 1);
        }
      } else {
        data.selectList.push(value.label);
        data.selectListNew.push(value.value);
      }
      data.selectList = data.selectList.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.selectListNew = data.selectListNew.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.labelIdlist = data.selectListNew.join(",");
      getList();
      //    }
    };

    // 行业 筛选点击第一层级
    const providerNewBtn = (value, type = "default", index, parvalue) => {
      if(type == 'default'){
        if (value.level == 2) {
        if (data.providerSelect.includes(value.label)) {
          if (data.providerSelect.length == 1 && data.vocationList.length > 2) {
            data.vocationList.splice(1, 1);
          } else if (data.providerSelect.length >= 2 && data.vocationList.length > 2) {
            if (data.vocationList[2].level) {
              data.vocationList.splice(2, 1);
            }
            data.vocationList.splice(1, 1);
          } else {}
          data.providerSelect = [];
          data.selectListOld = [];
        } else {
          if (data.vocationList[1].level) {
            if (data.providerSelect.length == 2) {
              if (data.vocationList[2].level) {
                data.vocationList.splice(2, 1);
              }
            } else if (data.providerSelect.length >= 3) {
              data.vocationList.splice(2, 1);
            } else {}
            data.providerSelect = [value.label];
            data.selectListOld = [value.value];
            data.vocationList[1] = value;
          } else {
            data.providerSelect.push(value.label);
            data.selectListOld.push(value.value);
            data.vocationList.splice(1, 0, value);
          }
        }
      }
      }else {
        console.log('value',value);
        if (value.level == 2) {
          
          if (data.citySelectList.includes(value.label)) {
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            if (data.citySelectList.length == 1 && data.vocationList.length > 2) {
              data.vocationList.splice(ins+1, 1);
            } else if (data.citySelectList.length >= 2 && data.vocationList.length > 2) {
              if (data.vocationList[ins+1].level) {
                data.vocationList.splice(ins+1, 1);
              }
              data.vocationList.splice(ins+1, 1);
            } else {}
            data.citySelectList = [];
            data.citySelectListOld = [];
          } else {
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            if (data.vocationList?.[ins+1]?.level) {
              console.log(data.citySelectList,'2222222');
              if (data.citySelectList.length == 2) {
                if (data.vocationList[ins+1].level) {
                  data.vocationList.splice(ins+1, 1);
                }
              } else if (data.citySelectList.length >= 3) {
                data.vocationList.splice(ins+2, 1);
              } else {}
              data.citySelectList = [value.label];
              data.citySelectListOld = [value.value];
              data.vocationList[ins+1] = value;
            } else {
              data.citySelectList.push(value.label);
              data.citySelectListOld.push(value.value);
              data.vocationList.push(value)
              // data.vocationList.splice(2, 0, value);
            }
          }
        }
      }
      
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }
    // 筛选点击第二层级
    const providerNewNewBtn = (value, type = "default", index, parvalue) => {
      if(type == 'default'){
        if (value.level == 3) {
          if (data.providerSelect.includes(value.label)) {
            // return
            if (data.providerSelect.length == 2 && value.children) {
              data.vocationList.splice(2, 1);
              data.providerSelect.splice(1, 1);
              data.selectListOld.splice(1, 1);
            } else if(data.providerSelect.length == 2 && !value.children){
              data.vocationList.splice(1, 1);
              data.providerSelect.splice(1, 1);
              data.selectListOld.splice(1, 1);
            } else if (data.providerSelect.length >= 3) {
              data.vocationList.splice(2, 1);
              data.providerSelect.splice(2, 1);
              data.providerSelect.splice(1, 1);
              data.selectListOld.splice(2, 1);
              data.selectListOld.splice(1, 1);
            } else {}
          } else {
            if (data.vocationList[2].level) {
              data.providerSelect = [data.providerSelect[0], value.label];
              data.selectListOld = [data.selectListOld[0], value.value];
              if (value.children) {
                data.vocationList[2] = value;
              } else {
                data.vocationList.splice(2, 1);
              }
            } else {
              data.providerSelect = [data.providerSelect[0], value.label];
              data.selectListOld = [data.selectListOld[0], value.value];
              if (value.children) {
                data.vocationList.splice(2, 0, value);
              }
            }
          }
        }
      }else{
        if (value.level == 3) {
          if (data.citySelectList.includes(value.label)) {
            // return
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            if (data.citySelectList.length == 2 && value.children) {
              data.vocationList.splice(ins+2, 1);
              data.citySelectList.splice(1, 1);
              data.citySelectListOld.splice(1, 1);
            } else if(data.citySelectList.length == 2 && !value.children){
              data.vocationList.splice(ins+1, 1);
              data.citySelectList.splice(1, 1);
              data.citySelectListOld.splice(1, 1);
            } else if (data.citySelectList.length >= 3) {
              data.vocationList.splice(ins+2, 1);
              data.citySelectList.splice(2, 1);
              data.citySelectList.splice(1, 1);
              data.citySelectListOld.splice(2, 1);
              data.citySelectListOld.splice(1, 1);
            } else {}
          } else {
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            if (data.vocationList?.[ins+2]?.level) {
              data.citySelectList = [data.citySelectList[0], value.label];
              data.citySelectListOld = [data.citySelectListOld[0], value.value];
              if (value.children) {
                data.vocationList[ins+2] = value;
              } else {
                data.vocationList.splice(ins+2, 1);
              }
            } else {
              data.citySelectList = [data.citySelectList[0], value.label];
              data.citySelectListOld = [data.citySelectListOld[0], value.value];
              if (value.children) {
                data.vocationList.push(value)
                // data.vocationList.splice(2, 0, value);
              }
            }
          }
        }
      }
      console.log('dataddd',data.vocationList);
      // console.log('citySelectList',data.citySelectList);
      // console.log('providerSelect',data.providerSelect);
      
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }
    // 筛选点击第三层级
    const providerNewNewNewBtn = (value, type = "default", index, parvalue) => {
      if(type == 'default'){
        if (value.level == 4) {
          if (data.providerSelect.includes(value.label)) {
            if (data.providerSelect.length == 3) {
              // data.vocationList.splice(3, 1);
              data.providerSelect.splice(2, 1);
              data.selectListOld.splice(2, 1);
            } else if (data.providerSelect.length == 4) {
              // data.vocationList.splice(3, 1);
              data.providerSelect.splice(3, 1);
              data.providerSelect.splice(2, 1);
              data.selectListOld.splice(3, 1);
              data.selectListOld.splice(2, 1);
            }
          } else {
            if (data.vocationList[3].level) {
              data.providerSelect[2] = value.label;
              data.selectListOld[2] = value.value;
              data.vocationList[3] = value;
            } else {
              data.providerSelect[2] = value.label;
              data.selectListOld[2] = value.value;
            }
          }
        }
      }else{
        if (value.level == 4) {
          if (data.citySelectList.includes(value.label)) {
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            // console.log('ins',ins);
            if (data.citySelectList.length == 3) {
              // data.vocationList.splice(3, 1);
              data.citySelectList.splice(ins+1, 1);
              data.citySelectListOld.splice(2, 1);
            } else if (data.citySelectList.length == 4) {
              // data.vocationList.splice(3, 1);
              data.citySelectList.splice(ins+2, 1);
              data.citySelectList.splice(2, 1);
              data.citySelectListOld.splice(3, 1);
              data.citySelectListOld.splice(2, 1);
            }
          } else {
            let ins = data.vocationList.findIndex(item=>{return item.label == '区域'})
            if (data.vocationList[ins+1].level) {
              data.citySelectList[2] = value.label;
              data.citySelectListOld[2] = value.value;
              // data.vocationList[ins+2] = value;
            } else {
              data.citySelectList[2] = value.label;
              data.citySelectListOld[2] = value.value;
            }
          }
        }
      }
      // console.log('citySelectList',data.citySelectList);
      // console.log('providerSelect',data.providerSelect);
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }

    const providerBtn = (value, type = "default", index, parvalue) => {
      if (value.children && type !== "last") {
        data.showId = value.value;
        data.showScense = "label";
      }
      if (type != "last") {
        data.activeKey = vocation.value = value.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.showLast = false;
        } else {
          if (value.children) {
            data.showLast = true;
          } else {
            data.showLast = false;
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.providerSelect.push(value.label);
          data.selectListOld.push(value.value);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      } else {
      	data.activeKey = parvalue.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [parvalue.label];
          data.selectListOld = [parvalue.value];
        } else {
          data.providerSelect = [parvalue.label];
          data.providerSelect.push(value.label);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = [parvalue.value];
          data.selectListOld.push(value.value);
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      // getList();
      if(data.switchOnOff){
        if(data.name != ''){
          aiFilter()
        }else{
          getList()
        }
      }else{
        getList()
      }
    };
    
    const providerEnter = (val, index) => {
    	data.showId = val.value;
    	data.showLast = true;
    }
    
    const providerLeave = (val, index) => {
    	data.showId = "";
    	data.showLast = false;
    }
    
    const deleteSelect = (_val, index, type) => {
      if (type == "pro") {
        let ins = data.vocationList.findIndex(item=>{return item.label == _val})
        let city = data.vocationList.findIndex(item=>{return item.label == '区域'})
        if(index == 2){
          data.vocationList.splice(city-1,1)
          data.providerSelect = [data.providerSelect[0],data.providerSelect[1]];
          data.selectListOld = [data.selectListOld[0],data.selectListOld[1]];
        }
        if (index == 1) {
          if(city > 2){
            data.vocationList.splice(ins-1,2)
          }else{
            data.vocationList.splice(ins-1,1)
          }
          data.providerSelect = [data.providerSelect[0]];
          data.selectListOld = [data.selectListOld[0]];
        }
        if (index == 0) {
          // return
          if(city > 2){
            data.vocationList.splice(1,2)
          }else{
            if(data.vocationList[city-1].label != '行业'){
              data.vocationList.splice(city-1,1)
            }
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.showLast = false;
        }
      }else{
        let ins = data.vocationList.findIndex(item=>{return item.label == _val})
        let city = data.vocationList.findIndex(item=>{return item.label == '区域'})
        if (index == 1) {
          data.vocationList.splice(ins,1)
          data.citySelectList = [data.citySelectList[0]];
          data.citySelectListOld = [data.citySelectListOld[0]];
        }
        if (index == 0) {
          if(city < data.vocationList.length-1){
            data.vocationList.splice(ins,1)
          }
          data.citySelectList = [];
          data.citySelectListOld = [];
          data.showLast = false;
        }
      }
      data.selectList.splice(index, 1);
      data.selectListNew.splice(index, 1);
      data.labelIdlist = data.selectListNew.join(",");
      if(data.switchOnOff){
        if(data.name != ''){
          aiFilter()
        }else{
          getList();
        }
      }else{
        getList();
      }
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showless = (type, _index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };
    const getAIList = () => {
    	if (data.name == "") {
    		data.showPagination = true;
    		getList();
    		return false
    	}

      data.activeKey = "";
      data.showId = '';
      data.showScense = "";
      data.showIndex = '';
      data.showLast = false;
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      data.selectListOld = [];

      data.loadingShow = true;
      data.AIsearch = true;
      AISearch({
        question: data.name,
        type: 7,
      }).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          getAIPageList();
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
          	item.image = item.image.split(",")[0];
            if (item.labelName) {
              item.labelName = item.labelName.split(",");
            }
            if (item.provider) {
              item.provider = item.provider.split("/")[1];
            }
            item.description =
              data.sourceType == "2" ? item.summary : item.description;
          });
        }
      });
    };
    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("solutionAllRefresh", refreshList);
    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent()
        }
      });
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    
    const getBoxTitle = (key) => {
    	const rect = box.value[key].getBoundingClientRect();
    	const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: (rect.x - selectHtml.x) + "px",
        top: (rect.y - selectHtml.y) + "px"
      };
    }

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: (firstRect.x - selectHtml.x) + "px",
        top: (rect.y - selectHtml.y + 49) + "px"
      };
    }

    const counterStore = useHomeStore();
    const changeContent = () => {
      if (data.newContentName == "手动定制") {
        data.newContentName = "方案查询";
      } else {
        data.newContentName = "手动定制";
      }
    };
    return {
      ...toRefs(data),
      aiFilter,
      box,
      selectData,
      vocation,
      labelSelect,
      backgroundStyles,
      getBoxLeft,
      getBoxTitle,
      counterStore,
      toggleShowLabel,
      providerNewBtn,
      providerNewNewBtn,
      providerNewNewNewBtn,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      tabChange,
      contentColor,
      sizeChange,
      contentLeave,
      add,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      labelChange,
      getAIList,
      refreshList,
      handleAudio,
      changeContent,
      providerEnter,
      providerLeave
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

::v-deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
