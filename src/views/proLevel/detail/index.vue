<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" :tip="loadShowTitle"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">产品</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="flex">
              <p class="title">{{ detailData.name }}</p>
            </div>
            <div class="left_middle">
              <div class="introduction">
                <a-tooltip overlayClassName="tooltip_class">
                  <template
                    v-if="isShowToolTip(detailData.introduction, 165)"
                    #title
                  >
                    {{ detailData.introduction }}</template
                  >
                  {{ detailData.introduction }}
                </a-tooltip>
              </div>
              <div class="flex">
                <div
                  class="label flex"
                  style="display: inline-block; margin-right: 12px"
                >
                  <div v-if="appMarket == 2">
                    <span
                      style="margin-right: 6px"
                      v-for="(item, key) in detailData.merchantMarketList[0]
                        .tagLabelList"
                      :key="key"
                    >
                      {{ item.name }}
                    </span>
                  </div>
                  <div v-if="appMarket == 4 && detailData.moveCloudMarketList">
                    <span
                      style="margin-right: 6px"
                      v-for="(item, key) in detailData.moveCloudMarketList[0]
                        .tagLabelList"
                      :key="key"
                    >
                      {{ item.name }}
                    </span>
                  </div>
                </div>
                <div class="flex info_bottom2 bottom1 tips">
                  <p
                    v-if="detailData.phone != null"
                    style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />{{ detailData.viewCount }}
                  </p>
                  <p
                    v-else
                    style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />-
                  </p>
                </div>
                <div class="apply_info">
                  应用市场：{{ markerDeal(appMarket) }}
                </div>
              </div>
              <div class="info_bottom" style="margin-top: 0px">
                <p>
                  联系人：{{
                    detailData.provider == "产品运营中心"
                      ? "陈超"
                      : detailData.contact
                  }}
                </p>
                <p>
                  联系电话：{{
                    detailData.provider == "产品运营中心"
                      ? "13912197039"
                      : dataDeal(detailData.phone)
                  }}
                </p>
                <p>
                  联系邮箱：
                  {{
                    detailData.provider == "产品运营中心"
                      ? "<EMAIL>"
                      : dataDeal(detailData.email)
                  }}
                </p>
              </div>
              <div class="info_bottom">
                <p>产品编码：{{ dataDeal(detailData.code) }}</p>
                <p>产品来源： {{ dataDeal(detailData.source) }}</p>
                <p>产品提供方：{{ detailData.provider }}</p>
              </div>

              <div class="info_bottom">
                <p v-if="appMarket == 2 || appMarket == 4">
                  产品分类：{{ dealClassify() }}
                </p>
                <p>首次上架： {{ detailData.shelfTime ? shelfTimeWith(detailData.shelfTime) : detailData.createTime }}</p>
                <p>最新更新： {{ detailData.editTime ? shelfTimeWith(detailData.editTime) : shelfTimeWith(detailData.shelfTime) }}</p>
              </div>
            </div>
          </div>
          <div class="right">
            <img
              :src="detailData.image"
              alt=""
              style="width: 100%; height: 100%"
              @click="imgShow(detailData.image)"
            />
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          :key="key"
          @click="handleClick"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
        <!-- 产品视频 -->
        <div class="card applyCard" id="#videoCard" v-if="detailData.video">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div
              class="flex function"
              style="justify-content: center; margin-top: 0"
            >
              <div
                style="
                  margin-bottom: 56px;
                  display: flex;
                  justify-content: center;
                "
                :style="videoData.style"
              >
                <video
                  id="video"
                  ref="videoRef"
                  :style="videoData.style"
                  controls
                  :src="detailData.video"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 功能特点 -->
        <div
          class="card applyCard"
          id="#advantageList"
          v-if="detailData.advantageList.length > 0"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">功能特点</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="flex function">
              <div
                class="card_list"
                v-for="(val, key) in detailData.advantageList"
                :key="key"
              >
                <div class="card_title">
                  <img src="@/assets/images/solution/detail/card_icon.png" />
                  {{ val.name }}
                </div>
                <div class="margin_t_12 desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(val.description, 100)"
                      #title
                      >{{ val.description }}</template
                    >
                    {{ val.description }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 应用行业 -->
        <div
          class="delivery"
          id="#appList"
          v-if="appMarket == 1 && textList.length > 0"
        >
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">应用行业</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">行业</div>
          </div>
          <div class="applyContent">
            <div
              class="flex align-center just-start"
              style="gap: 48px 60px; flex-wrap: wrap; margin-left: 70px"
            >
              <template v-for="(item, index) in textList" :key="index">
                <div class="matter">
                  <div
                    class="bac"
                    :style="{ backgroundImage: `url('${item.bac}')` }"
                  >
                    <img :src="item.activeImg" width="40px" height="40px" alt=""/>
                    <div class="text">{{ item.name }}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="card applyCard" id="#sceneList">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">应用场景</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
            <div class="cards">
              <div
                class="item_card"
                v-for="(item, key) in detailData.sceneList"
                :key="key + 1"
              >
                <img
                  v-if="`${item.cover}`"
                  v-lazy="`${item.cover}`"
                  alt=""
                  class="img"
                />
                <img
                  v-else
                  src="@/assets/images/ability/adlityDetail/apply.png"
                  class="img"
                  alt=""
                />
                <p class="title">{{ item.name }}</p>
                <p class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.description, 70)" #title>
                      {{ item.description }}</template
                    >
                    {{ item.description }}
                  </a-tooltip>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 交付形态 -->
        <!-- v-if="detailData.merchantMarketList && detailData.merchantMarketList[0].deliveryList.length > 0 && detailData.merchantMarketList[0].deliveryList[0].description != '' && appMarket == 2" -->
        <div class="delivery" id="#deliveryList" v-if="false">
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">交付形态</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">
              {{ appMarket == 2 ? "商客" : appMarket == 4 ? "移动云" : "" }}
            </div>
          </div>
          <div
            class="cards"
            v-if="
              detailData.merchantMarketList &&
              detailData.merchantMarketList[0].deliveryList.length > 0
            "
          >
            <div
              class="item_card"
              v-for="(item, key) in detailData.merchantMarketList[0]
                .deliveryList"
              :key="key + 1"
            >
              <img
                v-if="item.cover && item.cover != ''"
                v-lazy="`${item.cover}`"
                alt=""
                class="img"
              />
              <img
                v-else
                alt=""
                src="@/assets/images/ability/adlityDetail/apply.png"
                class="img"
              />
              <p class="title">{{ item.name }}</p>
              <p class="desc">
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.description, 70)" #title>
                    {{ item.description }}</template
                  >
                  {{ item.description }}
                </a-tooltip>
              </p>
            </div>
          </div>
        </div>

        <!-- hdict交付形态 -->
        <div
          class="delivery"
          id="#deliveryList"
          v-if="
            appMarket == 3 &&
            detailData.hdictMarketResList?.[0]?.deliveryList?.length > 0
          "
        >
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">交付形态</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">Hdict</div>
          </div>
          <div
            class="cards"
            v-if="
              detailData.hdictMarketResList &&
              detailData.hdictMarketResList[0].deliveryList.length > 0
            "
          >
            <div
              class="item_card"
              v-for="(item, key) in detailData.hdictMarketResList[0]
                .deliveryList"
              :key="key + 1"
            >
              <img
                v-if="item.cover && item.cover != ''"
                v-lazy="`${item.cover}`"
                alt=""
                class="img"
              />
              <img
                v-else
                src="@/assets/images/ability/adlityDetail/apply.png"
                alt=""
                class="img"
              />
              <p class="title">安装位置</p>
              <p class="desc">
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.description, 70)" #title>
                    {{ item.description }}</template
                  >
                  {{ item.description }}
                </a-tooltip>
              </p>
            </div>
          </div>
        </div>
        <div
          class="delivery"
          id="#deliveryList"
          v-if="
            detailData.moveCloudMarketList &&
            detailData.moveCloudMarketList[0].deliveryList.length > 0 &&
            detailData.moveCloudMarketList[0].deliveryList[0].description !=
              '' &&
            appMarket == 4
          "
        >
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">交付形态</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">{{ "移动云" }}</div>
          </div>
          <div
            class="cards"
            v-if="
              detailData.moveCloudMarketList &&
              detailData.moveCloudMarketList[0].deliveryList.length > 0
            "
          >
            <div
              class="item_card"
              v-for="(item, key) in detailData.moveCloudMarketList[0]
                .deliveryList"
              :key="key + 1"
            >
              <img
                v-if="item.cover && item.cover != ''"
                v-lazy="`${item.cover}`"
                alt=""
                class="img"
              />
              <img
                v-else
                src="@/assets/images/ability/adlityDetail/apply.png"
                alt=""
                class="img"
              />
              <p class="title">{{ item.name }}</p>
              <p class="desc">
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.description, 70)" #title>
                    {{ item.description }}</template
                  >
                  {{ item.description }}
                </a-tooltip>
              </p>
            </div>
          </div>
        </div>

        <!-- 资费套餐 -->
        <div class="delivery" id="#tariffList" v-if="appMarket == 2 && false">
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">资费套餐</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">商客</div>
          </div>
          <div class="combo" v-if="false">
            <template
              v-for="(item, index) in detailData.merchantMarketList[0]
                .tariffList"
              :key="index"
            >
              <img
                v-if="item.cover"
                width="176px"
                height="176px"
                :src="`${item.cover}`"
                alt=""
                @click="imgShow(item.cover)"
              />
              <div
                v-else
                style="
                  font-weight: 600;
                  font-size: 18px;
                  color: rgba(0, 0, 0, 0.65);
                "
              >
                根据客户定制需求，按实际工作量报价收费
              </div>
              <!-- <div class="proHead">
                <div class="proText"></div>
                <div class="proText">产品名称</div>
                <div class="proText">产品资费</div>
              </div>

              <div class="proContent">
                <div class="matter">
                  <div style="width: 33%">
                    <div class="package">{{ item.name }}</div>
                  </div>
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(item.specification, 20)"
                      #title
                    >
                      {{ item.specification }}
                    </template>
                    <div class="packName">{{ item.specification }}</div>
                  </a-tooltip>

                  <div class="packCmmb">
                    <span class="money">{{ item.tariff }}</span>
                    <span class="month">{{ item.unit }}</span>
                  </div>
                </div>
              </div> -->
            </template>
          </div>
        </div>

        <!-- 产品规格 -->
        <div
          class="card applyCard"
          id="#specsList"
          v-if="detailData.merchantMarketList?.[0]?.specsList?.length > 0"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">产品规格</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="bacInfo">商客</div>
            </div>
            <div class="cards-specsList">
              <div class="specsListBox">
                <div
                  class="specsList"
                  v-for="(item, key) in detailData.merchantMarketList[0]
                    .specsList"
                  :key="key + 1"
                >
                  <div class="specsListIndex">{{ key + 1 + "." }}</div>
                  <div class="specsListName">
                    {{ item.name + `（ ${item.code} ）` }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 落地案例 -->
        <div class="card applyCard" id="#caseList">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">落地案例</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div
              class="cards"
              v-for="(item, key) in detailData.caseList"
              :key="key + 1"
            >
              <img
                v-if="item.cover == '' || item.cover == null"
                src="@/assets/images/ability/adlityDetail/bac.png"
                alt=""
              />
              <img v-else v-lazy="`${item.cover}`" alt="" />
              <div class="right">
                <div
                  style="font-size: 18px; font-weight: 600; line-height: 40px"
                >
                  {{ item.name }}
                </div>
                <div class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(item.description, 250)"
                      #title
                    >
                      {{ item.description }}</template
                    >
                    {{ item.description }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- hdict产品参数 -->
        <div
          class="delivery"
          id="#productList"
          v-if="appMarket == 3 && detailData.hdictMarketResList"
        >
          <div class="card_content" style="padding-top: 40px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">产品参数</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="bacInfo">Hdict</div>
            </div>
            <div class="productContent">
              <div class="insideContent">
                <div class="proContain">
                  <template v-for="(item, index) in hdictParams" :key="index">
                    <div class="praramContent">
                      <div class="praramTitle">
                        参数名{{ index + 1 }}：{{ item.name || "-" }}
                        <div class="lineBot"></div>
                      </div>
                    </div>
                    <div class="praramContent">
                      <div class="praramTitle">
                        参数值{{ index + 1 }}：{{ item.value || "-" }}
                        <div class="lineBot"></div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Hdict资费套餐 -->
        <div
          class="delivery"
          id="#tariffList"
          v-if="
            appMarket == 3 &&
            detailData.hdictMarketResList?.[0]?.tariffList?.length > 0
          "
        >
          <div class="app_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">资费套餐</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="bacInfo">Hdict</div>
          </div>

          <div class="hdictComobo">
            <div class="proHead">
              <div class="proText"></div>
              <div class="proText">价格（元）</div>
              <div class="proText">单位</div>
            </div>
            <template
              v-for="(item, index) in detailData.hdictMarketResList[0]
                .tariffList"
              :key="index"
            >
              <div class="proContent">
                <div class="matter">
                  <div style="width: 33%">
                    <div class="package">套餐{{ toChinese(index + 1) }}</div>
                  </div>
                  <div class="packName">{{ item.tariff }}</div>
                  <div class="packCmmb">
                    <span class="month">{{ item.unit }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 附件 -->
        <div
          class="tab_content"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
        >
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">产品附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list">
          <li v-for="(item, key) in detailData.fileList" :key="key">
            <div class="li_box" @click="fileShow(item)">
              <div class="left_box">
                <img
                  src="@/assets/images/solution/detail/word.png"
                  alt=""
                  style="width: 40px; height: 40px"
                />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <div class="flex" style="align-items: center">
                <!-- <div class="flex linkIcon" @click.stop="shareLink(item)">
                  <LinkOutlined style="font-size: 22px; color: #2e7fff" />
                </div> -->
                <img
                  src="@/assets/images/solution/detail/download.png"
                  alt=""
                  @click.stop="downloadBtn(item.url)"
                  style="cursor: pointer"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
      <view-list :id="id" :type="type"></view-list>
      <img
        class="top"
        src="@/assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
    </div>
  </div>
  <a-modal
    width="1200px"
    :visible="imgVisible"
    :footer="null"
    @cancel="imgCancel"
  >
    <img alt="example" style="width: 100%" v-lazy="previewImage" />
  </a-modal>
  <a-modal
    v-model:visible="showDownloadModal"
    title="" :closable="false"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="450px"
  >
    <promptBox
      @downloadModalCancel="downloadModalCancel"
      @downloadModalConfirm="downloadModalConfirm"
      :msgObj="msgObj"
    />
  </a-modal>
  <a-modal
    v-model:visible="showDownloadForm"
    title="新增工单"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="600px"
  >
    <reviewForm
      @downloadFormCancel="downloadFormCancel"
      @downloadFormConfirm="downloadFormConfirm"
    />
  </a-modal>
  <a-modal
    v-model:visible="shareVisable"
    title="分享链接"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="500px"
    @cancel="shareVisableClose"
  >
    <div class="shareBody">
      <div class="shareContent">
        <LinkOutlined style="font-size: 26px" />
        <div class="shareContent-link">
          <div :title="`${shareTitle}方案`">{{ shareTitle }}方案</div>
          <div :title="link">{{ link }}</div>
        </div>
      </div>
      <div class="copyLink">
        <div @click="copy">复制链接</div>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { isShowToolTip } from "../../../utils/index.js";
import { useRouter, useRoute } from "vue-router";
import {
  getDetail,
  cancelCollect,
  collect,
} from "@/api/product/detail.js";
import precinct from "@/assets/images/product/precinct/party.png";
import partyActive from "@/assets/images/product/precinct/partyActive.png";
import policy from "@/assets/images/product/precinct/policy.png";
import policyActive from "@/assets/images/product/precinct/policyActive.png";
import industry from "@/assets/images/product/precinct/industry.png";
import industryActive from "@/assets/images/product/precinct/industryActive.png";
import traffic from "@/assets/images/product/precinct/traffic.png";
import trafficActive from "@/assets/images/product/precinct/trafficActive.png";
import medical from "@/assets/images/product/precinct/medical.png";
import medicalActive from "@/assets/images/product/precinct/medicalActive.png";
import teach from "@/assets/images/product/precinct/teach.png";
import teachActive from "@/assets/images/product/precinct/teachActive.png";
import newCard from "@/assets/images/product/precinct/new.png";
import newActive from "@/assets/images/product/precinct/newActive.png";
import finace from "@/assets/images/product/precinct/finace.png";
import finaceActive from "@/assets/images/product/precinct/finaceActive.png";
import farmer from "@/assets/images/product/precinct/farmer.png";
import farmerActive from "@/assets/images/product/precinct/farmerActive.png";
import internet from "@/assets/images/product/precinct/internet.png";
import internetActive from "@/assets/images/product/precinct/internetActive.png";
import ves from "@/assets/images/product/precinct/ves.png";
import vesActive from "@/assets/images/product/precinct/vesActive.png";
import level from "@/assets/images/product/precinct/level.png";
import levelActive from "@/assets/images/product/precinct/levelActive.png";
import rest from "@/assets/images/product/precinct/rest.png";
import restActive from "@/assets/images/product/precinct/restActive.png";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import viewList from "./viewList.vue";
import eventBus from "@/utils/eventBus";
import { toChinese } from "@/utils/index.js";
import { addShop } from "@/api/buyList/index.js";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import market from "./market.vue";
import { getNewDownCount } from "@/api/solutionNew/detail";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
import { LinkOutlined } from "@ant-design/icons-vue";
import clipboard3 from "vue-clipboard3";

export default defineComponent({
  components: {
    viewList,
    market,
    promptBox,
    reviewForm,
    LinkOutlined,
  },
  setup() {
    const route = useRoute();
    const Router = useRouter();
    const roleKey = JSON.parse(localStorage.getItem("userInfo")).roleKeyList;
    const data = reactive({
      msgObj:'',
      shareTitle: "",
      shareVisable: false,
      link: "",
      imgVisible: false,
      previewImage: null,
      loadShow: false,
      loadShowTitle: "附件加载中",
      functionKey: 1,
      caseKey: 1,
      isActive: 0,
      applyIndustryName: "",
      craftData: {},
      marketShow:
        roleKey.includes("customerManager") ||
        roleKey.includes("industryManager") ||
        roleKey.includes("sysAdmin"),
      collectActive: false,
      detailData: {
        deliveryList: [],
        advantageList: [],
        merchantMarketList: [
          {
            classifyName: "",
            deliveryList: [],
            tariffList: [],
            tagLabelList: [],
          },
        ],
        moveCloudMarketList: [
          {
            classifyName: "",
            deliveryList: [],
            tariffList: [],
            tagLabelList: [],
          },
        ],
      },
      // 话术根据角色显示隐藏
      anchorList: [
        {
          key: "advantageList",
          href: "#advantageList",
          title: "功能特点",
        },
        {
          key: "sceneList",
          href: "#sceneList",
          title: "应用场景",
        },
        { key: "download", href: "#download", title: "产品附件", show: true },
      ],
      tabList: [],
      textList: [],
      hdictParams: [],
      appList: [
        {
          name: "党政",
          bac: precinct,
          activeImg: partyActive,
        },
        {
          name: "政法公安",
          bac: policy,
          activeImg: policyActive,
        },
        {
          name: "工业",
          bac: industry,
          activeImg: industryActive,
        },
        {
          name: "交通",
          bac: traffic,
          activeImg: trafficActive,
        },
        {
          name: "医疗",
          bac: medical,
          activeImg: medicalActive,
        },
        {
          name: "教育",
          bac: teach,
          activeImg: teachActive,
        },
        {
          name: "创新融合",
          bac: newCard,
          activeImg: newActive,
        },
        {
          name: "金融",
          bac: finace,
          activeImg: finaceActive,
        },
        {
          name: "农商",
          bac: farmer,
          activeImg: farmerActive,
        },
        {
          name: "互联网",
          bac: internet,
          activeImg: internetActive,
        },
        {
          name: "信创",
          bac: ves,
          activeImg: vesActive,
        },
        {
          name: "低空经济",
          bac: level,
          activeImg: levelActive,
        },
        {
          name: "其他",
          bac: rest,
          activeImg: restActive,
        },
      ],
      id: route.query.id,
      currentAnchor: "#desc",
      isShow: "#desc",
      showTariff: false,
      type: "4",
      appMarket: route.query.appMarket,
      showDownloadModal: false,
      showDownloadForm: false,
    });

    const markerDeal = (val) => {
      let type = {
        1: "行业市场",
        2: "商客市场",
        3: "Hdict市场",
        4: "移动云市场",
        5: "-",
      };
      return type[val] || "-";
    };

    onMounted(() => {
      if (!data.marketShow) {
        data.anchorList = data.anchorList.filter(
          (item) => item.key !== "marketingList"
        );
      }
      setTimeout(() => {
        const videoDom = document.getElementById("video");
        if (videoDom.videoHeight > videoDom.videoWidth) {
          videoData.style = "height:400px;";
        } else {
          videoData.style = "width: 100%;";
        }
      }, 2000);
    });

    const shareLink = (item) => {
      data.loadShow = true;
      data.loadShowTitle = "生成链接中";
      data.shareTitle = `【麒麟平台】${item.name}`;
      let fileType = item.name.split(".")[1];
      if (fileType == "pdf") {
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = item.url;
        if(item.url.includes(windowOrigin)){
          newHref = item.url.split(windowOrigin)[1]
        }else{
          let url = item.url.split('/portal')
          newHref = url[1]
        }
        data.link = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(
          windowOrigin + newHref + "?token=" + token
        )}&urlName=${item.name}`;
        data.loadShow = false;
        data.shareVisable = true;
      } else {
        pptTopdf({
          filePath: item.path,
          fileUrl: item.url,
        }).then((res) => {
          if (res.code == 200) {
            let windowOrigin = window.location.origin;
            let token = localStorage.getItem("token");
            let newHref = res.data;
            if(res.data.includes(windowOrigin)){
      	      newHref = res.data.split(windowOrigin)[1]
            }else{
              let url = res.data.split('/portal')
              newHref = url[1]
            }
            console.log('=====','http://**************:8081' + newHref);
            
            data.link = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(
              'http://**************:8081' + newHref
            )}&urlName=${item.name}`;
            data.loadShow = false;
            data.shareVisable = true;
          } else {
            data.loadShow = false;
          }
        });
        return false;
      }
    };
    const copy = async () => {
      const { toClipboard } = clipboard3();
      await toClipboard(data.link);
      // navigator.clipboard.writeText(data.link)
      message.success("复制成功");
    };
    const shareVisableClose = () => {
      data.shareVisable = false;
      data.link = "";
      data.shareTitle = "";
    };

    const back = () => {
      Router.back(-1);
    };

    const videoData = reactive({
      style: "position: fixed;right:200%;",
    });

    const change = (v) => {
      data.isActive = v;
    };

    const getData = async () => {
      if (route.query.id) {
        await getDetail(route.query.id)
          .then((res) => {
            let date = new Date(res.data.createTime);
            let Y = date.getFullYear();
            let M =
              date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1;
            let D =
              (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) +
              " ";
            let GMT = Y + "-" + M + "-" + D;
            res.data.createTime = GMT;
            if (
              res.data.merchantMarketList &&
              res.data.merchantMarketList[0].tariffList &&
              res.data.merchantMarketList[0].tariffList.length > 0 &&
              res.data.merchantMarketList[0].tariffList[0].cover &&
              res.data.merchantMarketList[0].tariffList[0].cover != ""
            ) {
              data.showTariff = true;
            }
            // res.data.provider = res.data.provider.split("/")[1];
            let classifyList = [];
            if (res.data.classifyName) classifyList.push(res.data.classifyName);
            if (res.data.typeName) classifyList.push(res.data.typeName);
            if (res.data.labelName) classifyList.push(res.data.labelName);
            data.detailData = res.data;
            data.craftData = res.data;
            data.applyIndustryName = res.data.applyIndustryName;
            data.detailData.classifyList = classifyList.join("/");
            if (
              res.data.hdictMarketResList &&
              res.data.hdictMarketResList.length > 0
            ) {
              data.hdictParams = res.data.hdictMarketResList[0].parameterList;
            }
            if (
              res.data.industryMarketList &&
              res.data.industryMarketList.length > 0
            ) {
              let text =
                res.data.industryMarketList[0].applyIndustryName.split(",");
              data.appList.filter((item) => {
                text.forEach((val) => {
                  if (item.name == val) {
                    return data.textList.push(item);
                  }
                });
              });
            }
            if (
              res.data.industryMarketList &&
              res.data.industryMarketList[0].applyIndustryId
                .split(",")
                .includes("-1")
            ) {
              data.textList.push({
                name: "通用",
                bac: rest,
                activeImg: restActive,
              });
            }
            if (data.appMarket == 1) {
              data.anchorList = [
                {
                  key: "advantageList",
                  href: "#advantageList",
                  title: "功能特点",
                },
                {
                  key: "appList",
                  href: "#appList",
                  title: "应用行业",
                },
                {
                  key: "sceneList",
                  href: "#sceneList",
                  title: "应用场景",
                },
                {
                  key: "caseList",
                  href: "#caseList",
                  title: "落地案例",
                },
                // {
                //   key: "marketingList",
                //   href: "#marketingList",
                //   title: "营销话术",
                // },
                // {
                //   key: "orderList",
                //   href: "#orderList",
                //   title: "订购操作",
                // },
                {
                  key: "download",
                  href: "#download",
                  title: "产品附件",
                  show: true,
                },
              ];
            }
            if (data.appMarket == 2) {
              if (
                res.data.merchantMarketList &&
                res.data.merchantMarketList[0].deliveryList[0] &&
                res.data.merchantMarketList[0].deliveryList[0].description ==
                  "" &&
                res.data.merchantMarketList[0].deliveryList[0].name != ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  // {
                  //   key: "deliveryList",
                  //   href: "#deliveryList",
                  //   title: "交付形态",
                  // },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "specsList",
                    href: "#specsList",
                    title: "产品规格",
                  },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else if (
                res.data.merchantMarketList &&
                res.data.merchantMarketList[0].deliveryList[0] &&
                res.data.merchantMarketList[0].deliveryList[0].description != ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  // {
                  //   key: "deliveryList",
                  //   href: "#deliveryList",
                  //   title: "交付形态",
                  // },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "specsList",
                    href: "#specsList",
                    title: "产品规格",
                  },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else if (
                res.data.merchantMarketList &&
                res.data.merchantMarketList[0].deliveryList[0] &&
                res.data.merchantMarketList[0].deliveryList[0].description == ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  {
                    key: "specsList",
                    href: "#specsList",
                    title: "产品规格",
                  },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  // {
                  //   key: "deliveryList",
                  //   href: "#deliveryList",
                  //   title: "交付形态",
                  // },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "specsList",
                    href: "#specsList",
                    title: "产品规格",
                  },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              }

              if (res.data.merchantMarketList?.[0]?.specsList?.length == 0) {
                data.anchorList = data.anchorList.filter((item) => {
                  return item.key !== "specsList";
                });
              }
            }
            if (data.appMarket == 4) {
              if (
                res.data.moveCloudMarketList &&
                res.data.moveCloudMarketList[0].deliveryList[0] &&
                res.data.moveCloudMarketList[0].deliveryList[0].description ==
                  "" &&
                res.data.moveCloudMarketList[0].deliveryList[0].name != ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  // {
                  //   key: "deliveryList",
                  //   href: "#deliveryList",
                  //   title: "交付形态",
                  // },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else if (
                res.data.moveCloudMarketList &&
                res.data.moveCloudMarketList[0].deliveryList[0] &&
                res.data.moveCloudMarketList[0].deliveryList[0].description !=
                  ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  {
                    key: "deliveryList",
                    href: "#deliveryList",
                    title: "交付形态",
                  },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else if (
                res.data.moveCloudMarketList &&
                res.data.moveCloudMarketList[0].deliveryList[0] &&
                res.data.moveCloudMarketList[0].deliveryList[0].description ==
                  ""
              ) {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              } else {
                data.anchorList = [
                  {
                    key: "advantageList",
                    href: "#advantageList",
                    title: "功能特点",
                  },
                  {
                    key: "sceneList",
                    href: "#sceneList",
                    title: "应用场景",
                  },
                  {
                    key: "deliveryList",
                    href: "#deliveryList",
                    title: "交付形态",
                  },
                  // {
                  //   key: "tariffList",
                  //   href: "#tariffList",
                  //   title: "资费套餐",
                  // },
                  {
                    key: "caseList",
                    href: "#caseList",
                    title: "落地案例",
                  },
                  {
                    key: "download",
                    href: "#download",
                    title: "产品附件",
                    show: true,
                  },
                ];
              }
            }
            if (data.textList.length < 1) {
              data.anchorList = data.anchorList.filter(
                (item) => item.key !== "appList"
              );
            }

            if (!data.showTariff) {
              data.anchorList = data.anchorList.filter(
                (item) => item.key !== "tariffList"
              );
            }
            if (
              data.appMarket == 2 &&
              data.detailData.merchantMarketList &&
              data.detailData.merchantMarketList[0].deliveryList.length > 0
            ) {
              if (
                data.detailData.merchantMarketList[0].deliveryList.name != ""
              ) {
                data.anchorList = data.anchorList.filter(
                  (item) => item.key !== "deliveryList"
                );
              }
            }
            if (
              data.appMarket == 4 &&
              data.detailData.moveCloudMarketList &&
              data.detailData.moveCloudMarketList[0].deliveryList.length > 0
            ) {
              if (
                data.detailData.moveCloudMarketList[0].deliveryList.name != ""
              ) {
                data.anchorList = data.anchorList.filter(
                  (item) => item.key !== "deliveryList"
                );
              }
            }
            if (
              !data.detailData.merchantMarketList ||
              !data.detailData.moveCloudMarketList
            ) {
              data.anchorList = data.anchorList.filter(
                (item) => item.key !== "deliveryList"
              );
              data.anchorList = data.anchorList.filter(
                (item) => item.key !== "tariffList"
              );
            }

            data.detailData.fileList.forEach((item, index) => {
              if (item.type == 5 || item.type == 6) {
                data.detailData.video = item.url;
                data.detailData.fileList.splice(index, 1);
              }
            });

            if (data.appMarket == 3) {
              data.anchorList = [
                {
                  key: "advantageList",
                  href: "#advantageList",
                  title: "功能特点",
                },
                {
                  key: "sceneList",
                  href: "#sceneList",
                  title: "应用场景",
                },
                {
                  key: "deliveryList",
                  href: "#deliveryList",
                  title: "交付形态",
                },
                {
                  key: "caseList",
                  href: "#caseList",
                  title: "落地案例",
                },
                {
                  key: "productList",
                  href: "#productList",
                  title: "产品参数",
                },
                {
                  key: "tariffList",
                  href: "#tariffList",
                  title: "资费套餐",
                },
                {
                  key: "download",
                  href: "#download",
                  title: "产品附件",
                  show: true,
                },
              ];
              if (
                data.detailData.hdictMarketResList?.[0]?.deliveryList?.name != ""
              ) {
                data.anchorList = data.anchorList.filter(
                  (item) => item.key !== "deliveryList"
                );
              }
            }
            if (data.appMarket == 3 && !data.detailData.hdictMarketResList) {
              data.anchorList = data.anchorList.filter(
                (item) =>
                  item.key !== "deliveryList" &&
                  item.key !== "productList" &&
                  item.key !== "tariffList"
              );
            }
          })
          .catch(() => {
          });
      }
    };
    getData();

    const dataDeal = (value) => {
      if (value) return value;
      return "-";
    };
    const dealClassify = (value) => {
      if (data.appMarket == 2 && data.detailData.merchantMarketList)
        return data.detailData.merchantMarketList &&
          data.detailData.merchantMarketList[0].classifyName != "-"
          ? data.detailData.merchantMarketList[0].classifyName.replace(",", "/")
          : "-";
      if (data.appMarket == 4 && data.detailData.moveCloudMarketList)
        return data.detailData.moveCloudMarketList &&
          data.detailData.moveCloudMarketList[0].classifyName != "-"
          ? data.detailData.moveCloudMarketList[0].classifyName.replace(
              ",",
              "/"
            )
          : "-";
      return "-";
    };

    const collectById = () => {
      if (data.detailData.collect == 1) {
        cancelCollect(route.query.id)
          .then(() => {
            message.success("取消收藏成功");
            getData();
          })
          .catch(() => {
          });
      } else {
        collect(route.query.id)
          .then(() => {
            message.success("收藏成功");
            getData();
          })
          .catch(() => {
          });
      }
      data.collectActive = !data.collectActive;
    };

    const getCurrentAnchor = () => {
      return data.currentAnchor;
    };

    const scrollUp = () => {
      data.currentAnchor = "#desc";
      getCurrentAnchor();
      data.isActive = 0;
      document.getElementById("layout_content").scrollTo({
        top: 0,
        behavior: "smooth",
      });
    };
    const handleClick = (e, link) => {
      const href = link.href.replace("#", "");
      e.preventDefault();
      data.currentAnchor = "#" + href;
      let srcolls = document.getElementById(link.href);
      srcolls &&
        srcolls.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
      data.isShow = href;
    };
    const downloadBtn = (e) => {
      getNewDownCount({
        businessId: route.query.id,
        businessType: 4,
      }).then((res) => {
        if (res.data) {
          const href = e;
          let windowOrigin = window.location.origin;
          let token = localStorage.getItem("token");
          let newHref = href;
          if (href.includes(windowOrigin)) {
            newHref = "/portal" + href.split(windowOrigin)[1];
          }
          window.open(windowOrigin + newHref + "?token=" + token);
        } else {
          if(res.msg.includes('5')){
            data.msgObj = {
              applyTimes:1,
              msg:res.msg,
              fullPath:route.fullPath
            }
          }else{
            data.msgObj = {
              applyTimes:2,
              msg:res.msg,
              fullPath:route.fullPath
            }
          }
          data.showDownloadModal = true;
        }
      });
      //data.loadShow = true;

      // return false;
      // axios
      //   .get(href, { responseType: "blob" })
      //   .then((res) => {
      //     data.loadShow = false;
      //     const blob = new Blob([res.data]);
      //     const link = document.createElement("a");
      //     link.href = URL.createObjectURL(blob);
      //     link.download = downName;
      //     link.click();
      //     URL.revokeObjectURL(link.href);
      //   })
      //   .catch(console.error);
    };

    const fileShow = (val) => {
      let fileType = val.name.split(".")[1];
      if (fileType == "pdf") {
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = val.url;
        if (val.url.includes(windowOrigin)) {
          newHref = "/portal" + val.url.split(windowOrigin)[1];
        }
        const newpage = Router.resolve({
          name: "lookPdf",
          query: {
            urlMsg: encodeURIComponent(
              windowOrigin + newHref + "?token=" + token
            ),
            urlName: val.name,
          },
        });
        window.open(newpage.href, "_blank");
      } else {
        data.loadShow = true;
        data.loadShowTitle = "附件加载中";
        pptTopdf({
          filePath: val.path,
          fileUrl: val.url,
        }).then((res) => {
          data.loadShow = false;
          if (res.code == 200) {
            let windowOrigin = window.location.origin;
            let token = localStorage.getItem("token");
            let newHref = res.data;
            if (res.data.includes(windowOrigin)) {
              newHref = "/portal" + res.data.split(windowOrigin)[1];
            }
            const newpage = Router.resolve({
              name: "lookPdf",
              query: {
                urlMsg: encodeURIComponent(
                  windowOrigin + newHref + "?token=" + token
                ),
                urlName: val.name,
              },
            });
            window.open(newpage.href, "_blank");
          }
        });
        return false;
      }
    };
    const toBuy = () => {
      addShop({ productId: route.query.id, type: "2" })
        .then(() => {
          eventBus.emit("cartRefresh");
          getData();
        })
        .catch(() => {
        });
    };

    const toShop = () => {
      addShoppingCart({
        schemeId: route.query.id,
        type: "4",
      }).then(() => {
        eventBus.emit("cartRefresh");
        getData();
      });
    };

    const imgShow = (url) => {
      data.imgVisible = true;
      data.previewImage = url;
    };

    const imgCancel = () => {
      data.imgVisible = false;
    };
    const dealData = (e) => {
      if (e === undefined || e === null || e === "" || e >= 8888) {
        return "以具体业务定价为准";
      } else {
        return e;
      }
    };
    eventBus.on("productDetailRefresh", getData);
    // 下载超限提示弹窗取消按钮
    const downloadModalCancel = () => {
      data.showDownloadModal = false;
    };
    // 下载超限提示弹窗确认按钮
    const downloadModalConfirm = () => {
      data.showDownloadModal = false;
      data.showDownloadForm = true;
    };
    const downloadFormCancel = () => {
      data.showDownloadForm = false;
    };
    const downloadFormConfirm = () => {
      data.showDownloadForm = false;
    };
    const shelfTimeWith = (value) => {
      if (value) {
        return value.slice(0, 10);
      }
      return "-";
    };
    return {
      ...toRefs(data),
      shelfTimeWith,
      collectById,
      downloadBtn,
      toBuy,
      toShop,
      fileShow,
      shareLink,
      copy,
      shareVisableClose,
      back,
      scrollUp,
      markerDeal,
      toChinese,
      handleClick,
      dataDeal,
      dealClassify,
      dealData,
      change,
      isShowToolTip,
      imgShow,
      videoData,
      imgCancel,
      downloadModalCancel,
      downloadModalConfirm,
      downloadFormCancel,
      downloadFormConfirm,
    };
  },
});
</script>

<style lang="scss" scoped>
.linkIcon {
  background-color: #fff;
  width: 37px;
  height: 37px;
  border-radius: 18px;
  justify-content: center;
  align-items: center;
  margin-bottom: 7px;
}
.shareBody {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .shareContent {
    flex: 1;
    display: flex;
    align-items: center;
    > img {
      width: 32px;
      height: 32px;
    }
    > .shareContent-link {
      min-width: 160px;
      max-width: 320px;
      > div {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div:last-child {
        text-indent: 0.6em;
      }
    }
  }
  .copyLink {
    width: 74px;
    padding: 4px 9px;
    border-radius: 8px;
    background-color: #96cfed;
  }
}
@import "./index.scss";
</style>

<style lang="scss">
::v-deep(.ant-modal-body) {
  width: 1000px;
}

.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: #1a66fb;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.display {
  display: none !important;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}

.cards-specsList {
  margin-top: 24px;
  padding: 32px 52px;
  background: linear-gradient(163deg, #fbfcfc 0%, #fbfcfc 38%, #ffffff 100%);
  box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
    -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
  border-radius: 16px;

  .specsListBox {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .specsList {
      width: 47%;
      margin: 12px 0;
      display: flex;
      align-items: center;
      height: 52px;
      box-shadow: 0px 4px 24px 0px rgba(31, 68, 148, 0.12);
      border-radius: 8px;
      .specsListIndex {
        width: 52px;
        border-radius: 8px;
        line-height: 52px;
        text-align: center;
        font-weight: 500;
        font-size: 18px;
        color: #2e3852;
        background-color: rgba(195, 218, 245, 0.5);
      }
      .specsListName {
        flex: 1;
        margin-left: 12px;
        background-color: #fff;
      }
    }
  }
}
</style>
