<template>
  <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
    <div
      style="
        min-height: 56px;
        background: #ffffff;
        justify-content: center;
        align-items: center;
      "
      class="flex"
    ></div>

    <div class="vocationPull" style="flex: 1; height: 56px">
      <div class="switch">
        <div class="AIlogo"></div>
        <a-switch
          checked-children="on"
          un-checked-children="off"
          v-model:checked="switchOnOff"
        />
      </div>
      <a-config-provider
        :locale="zhCN"
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
      >
        <a-input
          v-model:value="name"
          class="inputClass"
          allow-clear
          @keyup.enter="seekContent"
          placeholder="请输入产品名称、标签等关键词进行检索"
        />

        <div
          class="seekInfo"
          :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
          @click="seekContent"
        >
          <img src="@/assets/images/home/<USER>" alt="" />
          <div>搜索</div>
        </div>
      </a-config-provider>
    </div>
  </div>
  <div class="newLoading" v-if="AIsearch">
    <loadingSmall />
  </div>
  <div class="selectData" ref="selectData" v-if="true || !switchOnOff">
    <div class="selcet_box">
      <div class="left_select">市场分类：</div>
      <div class="right_select">
        <span
          style="height: 49px"
          :class="{ activeBtn: activeMarket == item.value }"
          v-for="(item, key) in marketList"
          :key="key"
        >
          <div class="title" @click="marketBtn(item)">{{ item.name }}</div>
        </span>
      </div>
    </div>
   	<div
      :class="[
        'selcet_box',
        { showMore: showScense == 'label' && showIndex === index },
      ]"
      v-for="(val, index) in vocationList"
      :key="index"
    >
      <div class="left_select">{{ val.label }}：</div>
      <div
        :class="[
          'right_select',
          { showHidden: showScense == 'label' && showIndex === index },
        ]"
      >
        <span
          ref="box"
          v-if="val.type == 1"
          v-for="(value, key1) in val.children"
          :key="key1"
          :class="{ activeBtn: activeKey === value.value }"
          style="height: 49px"
        >
          <div
            class="title"
            @click="providerBtn(value, 'default', index)"
            @mouseenter="providerEnter(value, index)"
          >
            {{ value.label }}
          </div>
        </span>
        <span
          v-else
          v-for="(value, key2) in val.children"
          :key="key2"
          :class="{ activeBtn: selectList.indexOf(value.label) > -1 }"
          :style="{
            height: showLast && showId == value.value ? '89px' : '49px',
          }"
        >
          <div class="title" @click="labelSelect(value, 'default', index)">
            {{ value.label }}
          </div>
          <div class="last_data" v-if="showLast && showId == value.value">
            <span
              v-for="(e, i) in value.children"
              @click="labelSelect(e, 'last')"
              :class="{ activeBtn: selectList.indexOf(e.label) > -1 }"
              :key="i"
              style="width: 60px"
            >
              {{ e.label }}
            </span>
          </div>
        </span>
      </div>
      <span
        class="more flex"
        v-if="val.children && val.children.length >= 8 && showIndex !== index"
        @click="showMore('label', index)"
        >更多<img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
      <span
        class="more flex"
        v-if="val.children && val.children.length >= 8 && showIndex === index"
        @click="showless('label_less', index)"
        >收起<img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
    </div> 
    <div
      v-if="vocationList[0]"
      v-for="(value, key1) in vocationList[0].children"
      @mouseleave="providerLeave(value, key1)"
    >
      <div
        class="last_data_top"
        :style="getBoxTitle(key1)"
        v-if="showLast && showId == value.value"
        @click="providerBtn(value, 'default', key1)"
      >
        {{ value.label }}
      </div>
      <div
        class="last_data"
        v-if="showLast && showId == value.value"
        :style="getBoxLeft(key1)"
      >
        <!--{ left: -135 * key1 + 'px' }-->
        <span
          v-for="(e, i) in value.children"
          @click="providerBtn(e, 'last', i, value)"
          style="width: auto; padding: 11px 20px; cursor: pointer"
          :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }"
          :key="i"
        >
          {{ e.label }}
        </span>
      </div>
    </div>

    <div class="select_boot flex">
      <div>
        已选条件：
        <span v-if="activeMarket != ''">市场分类：</span>
        <span style="margin-left: 8px">{{ actMarket }}</span>
        <img
          v-if="activeMarket != ''"
          src="@/assets/images/solution/home/<USER>"
          alt=""
          style="width: 16px; height: 16px; cursor: pointer"
          @click="deleteMarket()"
        />
        <span v-if="providerSelect.length > 0">产品分类：</span>
        <span v-for="(val, index) in providerSelect" :key="index">
          <span style="margin-left: 8px">{{ val }}</span>
          <img
            src="@/assets/images/solution/home/<USER>"
            alt=""
            style="width: 16px; height: 16px; cursor: pointer"
            @click="deleteSelect(val, index, 'pro')"
          />
        </span>
        <span class="label" v-if="selectList.length > 0">功能标签：</span>
        <span v-for="(item, index) in selectList" :key="index">
          <span style="margin-left: 8px">{{ item }}</span>
          <img
            src="@/assets/images/solution/home/<USER>"
            alt=""
            style="width: 16px; height: 16px; cursor: pointer"
            @click="deleteSelect(item, index)"
          />
        </span>
      </div>
      <div class="right_con">
        共找到 <span>{{ totalItemCount }}</span> 条结果
      </div>
    </div>
  </div>
  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="AITips flex align-center" v-if="!showPagination">
        <img
          style="width: 40px; height: 40px; margin-right: 10px"
          src="../../../../../assets/images/AI/ai.png"
          alt=""
        />
        <div class="words">以下是AI助手为您找到的相关结果</div>
      </div>
      <div class="cardContent">
        <div class="card_total flex-1">
          <template v-for="(item, index) in tableList" :key="index">
            <div
              :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                },
              ]"
              @mouseenter="contentColor(index)"
              @mouseleave="contentLeave"
              @click="proDetail(item)"
            >
              <div style="display: flex; margin: 24px">
                <div>
                  <img
                    v-if="item.image"
                    v-lazy="`${item.image}`"
                    style="width: 168px; height: 105px"
                    alt=""
                  />
                  <img
                    src="@/assets/images/home/<USER>"
                    style="width: 168px; height: 105px"
                    alt=""
                    v-else
                  />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <!-- <a-tag color="#D7E6FF" v-if="item.labelName || item.typeName || item.classifyName">{{
                        item.labelName
                          ? item.labelName
                          : item.typeName
                          ? item.typeName
                          : item.classifyName
                      }}</a-tag> -->
                      <div class="card_title">{{ item.name }}</div>
                    </div>
                    <div class="flex" v-if="item.provider">
                      <a-tag
                        :bordered="false"
                        class="cityStyle"
                        style="margin-right: 0"
                        >{{ item.provider }}</a-tag
                      >
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.introduction }}
                  </div>
                  <div class="flex" style="justify-content: space-between">
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          v-for="(value,index) in item.market" :key="index"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ value }}</a-tag
                        >
                        <!-- <a-tag
                          color="#D7E6FF"
                          v-if="item.classifyName"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.classifyName }}</a-tag
                        > -->
                      </div>
                    </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    ></div>
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding-right: 6px;
                    "
                  >
                    <div style="display: flex; align-items: center">
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                        alt=""
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.viewCount"
                        >{{ item.viewCount }}</span
                      >
                      <span v-else>-</span>
                      <!-- <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px; margin-left: 18px"
                      /> -->
                      <!-- <span
                        v-if="item.existsPpt"
                        class="short-text market_text"
                        >{{ item.market }}</span
                      >
                      <span
                        class="long-text market_text"
                        v-else
                        >{{ item.market }}</span
                      > -->
                      <!-- <span v-else>-</span> -->
                    </div>
                    <!-- <div style="display: flex">
                      <div v-if="item.existsPpt">
                        <div v-if="item.addCart">
                          <button disabled class="cart-button">
                            <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                              &nbsp;已加入预选</span
                            >
                          </button>
                        </div>
                        <div v-else>
                          <button
                            class="cart-button pointer"
                            @click.stop="addShopping(item.id)"
                          >
                            <img
                              class="add-icon"
                              src=" @/assets/images/AI/isadded.png"
                            /><span class="add"> &nbsp;加入预选</span>
                          </button>
                        </div>
                      </div>
                      <div style="margin-left: 8px">
                        <div v-if="item.addOrder">
                          <button disabled class="cart-button">
                            <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                              &nbsp;已加入定制</span
                            >
                          </button>
                        </div>
                        <div v-else>
                          <button
                            class="cart-button pointer"
                            @click.stop="add(item.id)"
                          >
                            <img
                              class="add-icon"
                              src=" @/assets/images/AI/isadded.png"
                            /><span class="add"> &nbsp;加入定制</span>
                          </button>
                        </div>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination
          v-model:pageSize="pageItemSize"
          v-model:current="currentPage"
          :pageSizeOptions="pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="totalItemCount"
          @change="pageChange"
          @showSizeChange="sizeChange"
          class="mypage"
        />
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useRouter } from "vue-router";
import { getProductList,marketSelect } from "@/api/product/home";

import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getNewLabel } from "@/api/product/home";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { AISearch } from "@/api/AI/ai.js";
import { useHomeStore } from "@/store";
import { addShop } from "@/api/buyList/index.js";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";

export default defineComponent({
  components: { loadingSmall },
  setup() {
    const counterStore = useHomeStore();
    const baseURL = getMakeUrl();
    const vocation = ref("5");
    const region = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const data = reactive({
      name: "",
      moment: "",
      loadingShow: true,
      activeKey: "",
      activeMarket: "",
      actMarket: "",
      activeKeyAbility: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      AIsearch: false,
      labelIdlist: [],

      labelList: [],
      tableList: [],
      showLabel: true,
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      showMore: false,
      classSelect: "",
      typeSelect: "",
      labeSelect: "",
      showScense: "",
      morePro: true,
      labelPro: true,
      abilityPro: true,
      providerSelect: [],
      selectListOld: [],
      abilitySelect: [],
      showIndex: "",
      showPagination: true,
      switchOnOff: true,
      productType: "",
      productClassList: [],
      productTypeList: [],
      productLabelList: [],
      productLabel: "",
      tableAIAllList: [],
      filterArr:[],// ai搜索过滤用
      marketList: [
        {
          name: "通用产品",
          value: '0',
        },
        {
          name: "商客市场",
          value: 2,
        },
        {
          name: "Hdict市场",
          value: 3,
        },
        // {
        //   name: "移动云市场",
        //   value: 4,
        // },
      ],
      vocationList: [
        { label: "产品分类", value: 1, type: 1, length: 0, children: [] },
        // { label: "功能标签", value: 2, type: 2, length: 0, children: [] },
      ],
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        baseClassify: data.selectListOld.length == 2 ? data.selectListOld[1] : data.activeKey,
        applicationMarket: data.activeMarket,
        //labelIds: data.selectListOld.length == 2 ? data.selectListOld[1] : "",
        // typeIds: data.selectListOld.length == 2 ? data.selectListOld[1] : "",
        tabLabel: data.labelIdlist,
      };
      data.loadingShow = true;
      getProductList(pageParams)
        .then((res) => {
          data.showPagination = true;
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.provider = item.provider.split("/")[0];
            if (item.marketplaceList.length != 0) {
              let market = [];
              let marketSort = item.marketplaceList.map(val=>{
                return val.applicationMarket
              })
              marketSort.sort((a,b)=>a-b)
              // console.log('marketSort',marketSort);
              marketSort.map((ele) => {
                if (ele == 0) {
                  market.push("通用产品");
                }
                // if (ele == 1) {
                //   market.push("行业市场");
                // }
                if (ele == 2) {
                  market.push("商客市场");
                }
                if (ele == 3) {
                  market.push("Hdict市场");
                }
                if (ele == 4) {
                  // market.push("移动云市场");
                }
              });
              item.market = market;
            }
          });
          if (data.activeKey == "") {
            data.totalItemCount1 = res.data.totalRows;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    const getLabel = () => {
      let params = {
        type: 3,
        pageNo: 1,
        pageSize: 100,
      };
      getNewLabel(params).then((res) => {
        data.vocationList[0].length = res.data.rows.length;
        data.vocationList[0].children = res.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
                label: child.name,
                value: child.id,
                children: child.children
                  ? child.children.map((ele) => ({
                      label: ele.name,
                      value: ele.id,
                    }))
                  : undefined,
              }))
            : undefined,
        }));

//      let params = {
//        type: 2,
//        pageNo: 1,
//        pageSize: 100,
//      };
//      getNewLabel(params).then((res) => {
//        data.vocationList[1].length = res.data.rows.length;
//        data.vocationList[1].children = res.data.rows.map((item) => ({
//          label: item.name,
//          value: item.id,
//          length: item.children ? item.children.length : 0,
//          children: item.children
//            ? item.children.map((child) => ({
//                label: child.name,
//                value: child.id,
//              }))
//            : undefined,
//        }));
//      });
      });
    };
    getLabel();
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    getList();

    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };
    const regionChange = (val) => {
      region.value = val;
      getList();
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
        },
        name: "productDetail",
      });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (id) => {
      addShop({
        productId: id.toString(),
        type: "2",
      }).then((res) => {
        if (data.showPagination) {
          getList();
        } else {
          //getAIList();
        }
        eventBus.emit("cartRefresh");
        counterStore.contralShop = false;
      });
    };
    const addShopping = (id) => {
      addShoppingCart({
        schemeId: id.toString(),
        type: "4",
      }).then((res) => {
        if (data.showPagination) {
          getList();
        } else {
          //getAIList();
        }
        eventBus.emit("cartRefresh");
        counterStore.contralShop = false;
      });
    };

    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      console.log('data',data.activeKey);
      
      // if(data.activeKey != '' || data.activeMarket != ''){
      //   aiFilter()
      // }else{
      // }
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };

    const aiFilter = () => {
      if(data.activeMarket != "" && data.activeKey == ''){
        data.filterArr = data.tableAIAllList.filter(item=>{
          item.markets = item.marketplaceList.map(ids=>{
            return ids.applicationMarket
          })
          return item.markets.includes(data.activeMarket*1)
        })
      }
      if(data.activeKey != '' && data.activeMarket == ''){
        data.filterArr = data.tableAIAllList.filter(item=>{
          return item.baseClassify.includes(data.activeKey)
        })
      }
      if(data.activeMarket != '' && data.activeKey != ''){
        let arr = data.tableAIAllList.filter(item=>{
          return item.baseClassify.includes(data.activeKey)
        })
        data.filterArr = arr.filter(item=>{
          item.markets = item.marketplaceList.map(ids=>{
          return ids.applicationMarket
        })
          return item.markets.includes(data.activeMarket*1)
        })
      }
      if(data.activeMarket == '' && data.activeKey == ''){
        data.filterArr = data.tableAIAllList
      }
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      data.totalItemCount = data.filterArr.length
    }

    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
        if (data.selectList.includes(value.label)) {
          data.showLast = false;
        } else {
          data.showLast = true;
        }
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      }
      if (data.selectList.includes(value.label)) {
        const index = data.selectList.findIndex((item) => item === value.label);
        if (index !== -1) {
          data.selectList.splice(index, 1);
        }
        const index1 = data.selectListNew.findIndex(
          (item) => item === value.value
        );
        if (index1 !== -1) {
          data.selectListNew.splice(index, 1);
        }
      } else {
        data.selectList.push(value.label);
        data.selectListNew.push(value.value);
      }
      data.selectList = data.selectList.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.selectListNew = data.selectListNew.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      getList();
    };
    const providerBtn = (value, type = "default", index, parvalue) => {
      if (type == "last") data.showLast = false;
      if (value.children && type !== "last") {
        data.showId = value.value;
        data.showScense = "label";
        //data.showIndex = index;
      }
      data.currentPage = 1;
      if (type != "last") {
        data.activeKey = vocation.value = value.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.showLast = false;
        } else {
          if (value.children) {
            data.showLast = true;
          } else {
            data.showLast = false;
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.providerSelect.push(value.label);
          data.selectListOld.push(value.value);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          //
          data.labelIdlist = data.selectListNew.join(",");
        }
      } else {
        data.activeKey = parvalue.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [parvalue.label];
          data.selectListOld = [parvalue.value];
        } else {
          data.providerSelect = [parvalue.label];
          data.providerSelect.push(value.label);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = [parvalue.value];
          data.selectListOld.push(value.value);
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      }
      if(data.switchOnOff){
        if(data.name != ''){
          aiFilter()
        }else{
          data.currentPage = 1;
          getList();
        }
      }else{
        data.currentPage = 1;
        getList();
      }
    };

    const providerEnter = (val, index) => {
      data.showId = val.value;
      data.showLast = true;
    };

    const providerLeave = (val, index) => {
      data.showId = "";
      data.showLast = false;
    };

    const providerClick = (val, index) => {
      if (data.providerName == val.label) {
        data.providerName = "";
      } else {
        data.providerName = val.label;
      }
      data.currentPage = 1;
      getList();
    };

    const deleteSelect = (val, index, type) => {
      if (type == "provider") {
        data.providerName = "";
        data.currentPage = 1;
        if(data.switchOnOff){
          if(data.name != ''){
            aiFilter()
          }else{
            getList()
          }
        }else{
          getList();
        }
      } else {
        if (type == "pro") {
          if (index == 1) {
            data.providerSelect = [data.providerSelect[0]];
            data.selectListOld = [data.selectListOld[0]];
          } else if (index == 0) {
            data.providerSelect = [];
            data.selectListOld = [];
            data.activeKey = "";
            data.showLast = false;
          }
        }
        data.selectList.splice(index, 1);
        data.selectListNew.splice(index, 1);
        data.labelIdlist = data.selectListNew.join(",");
        data.currentPage = 1;
        if(data.switchOnOff){
          if(data.name != ''){
            aiFilter()
          }else{
            getList()
          }
        }else{
          getList();
        }
      }
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
        data.showLast = true;
      }
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
        data.showLast = false;
      }
    };

    const getAIList = () => {
      if (data.name == "") {
        data.showPagination = true;
        data.currentPage = 1;
        getList();
        return false;
      }

      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      data.selectListOld = [];
      data.activeKey = "";
      data.activeMarket = ""
      data.actMarket = ''
      data.showLast = false;
      
      data.loadingShow = true;
      data.AIsearch = true;
      AISearch({
        question: data.name,
        type: 6,
      }).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableList = [];
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          getAIPageList();
          // data.tableList = data.tableAIAllList.slice(0, 10);
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
            item.provider = item.provider.split("/")[0];
            if (item.marketplaceList.length != 0) {
                let market = [];
                let marketSort = item.marketplaceList.map(val=>{
                  return val.applicationMarket
                })
                marketSort.sort((a,b)=>a-b)
                // console.log('marketSort',marketSort);
                marketSort.map((ele) => {
                  if (ele == 0) {
                    market.push("通用产品");
                  }
                  // if (ele == 1) {
                  //   market.push("行业市场");
                  // }
                  if (ele == 2) {
                    market.push("商客市场");
                  }
                  if (ele == 3) {
                    market.push("Hdict市场");
                  }
                  if (ele == 4) {
                    // market.push("移动云市场");
                  }
                });
                item.market = market;
            }
          });
        }
      });
    };
    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };

    eventBus.on("productRefresh", refreshList);

    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };

    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };
    const marketTree = (i)=>{
      let params = {
        type:1,
        applicationMarkets:i
      }
      marketSelect(params).then(res=>{
        data.vocationList[0].length = res.data.length;
        data.vocationList[0].children = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
                label: child.name,
                value: child.id,
                children: child.children
                  ? child.children.map((ele) => ({
                      label: ele.name,
                      value: ele.id,
                    }))
                  : undefined,
              }))
            : undefined,
        }));
      })
    }
    const marketBtn = (item) => {
      data.currentPage = 1;
      if (data.activeMarket == item.value) {
        data.activeMarket = "";
        data.actMarket = "";
        if(data.switchOnOff){
          if(data.name != ''){
            aiFilter()
          }else{
            data.currentPage = 1;
            getList();
          }
        }else{
          data.currentPage = 1;
          getList();
        }
      } else {
        data.activeMarket = item.value;
        data.actMarket = item.name;
        if(data.switchOnOff){
          let id = item.value
          if(data.name != ''){
            aiFilter()
          }else{
            data.currentPage = 1;
            getList();
          }
        }else{
          data.currentPage = 1;
          getList();
        }
      }
      // marketTree(data.activeMarket);
    };
    const deleteMarket = () => {
      data.activeMarket = "";
      data.actMarket = "";
      if(data.switchOnOff){
        aiFilter()
      }else{
        getList();
      }
      // marketTree(data.activeMarket);
    };
    return {
      ...toRefs(data),
      vocation,
      deleteMarket,
      marketBtn,
      toggleShowLabel,
      region,
      labelSelect,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      regionChange,
      tabChange,
      add,
      addShopping,
      contentColor,
      labelChange,
      getLabel,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      getAIList,
      refreshList,
      counterStore,
      getBoxTitle,
      getBoxLeft,
      providerLeave,
      providerEnter,
      box,
      selectData,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none !important;
}

.custom-placeholder .ant-select-selection-placeholder {
  color: #000;
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
