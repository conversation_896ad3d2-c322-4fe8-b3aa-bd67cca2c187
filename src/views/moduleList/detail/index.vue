<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">能力</span>
        <span class="title"> / </span>
        <!-- <span
          class="current"
          v-if="detailData.sceneName != '' && detailData.sceneName != null"
          >{{ detailData.sceneName }}</span
        > -->
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            alt=""
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            alt=""
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <div class="title">{{ detailData.name }}</div>
              <a-tag class="code">能力编码：{{ detailData.code }}</a-tag>
              <!--<div style="display: inline-block">
                <div class="score">
                  <div class="scoreIcon">
                    <img src="../../../assets/images/score.png" alt="" />
                  </div>
                  <span class="scoreTitle">能力评分：</span>
                  <span class="scoreNum">{{ dealData(detailData.score) }}</span>
                </div>
              </div>-->
            </div>
            <div class="left_middle">
              <p class="info">
                <a-tooltip overlayClassName="tooltip_class">
                  <template
                    v-if="isShowToolTip(detailData.abilityIntro, 174)"
                    #title
                    >{{ detailData.abilityIntro }}</template
                  >
                  {{ detailData.abilityIntro }}
                </a-tooltip>
              </p>
              <div class="bottom1">
                <p v-if="detailData.phone != null">
                  <img
                    src="@/assets/images/solution/detail/eyes.png"
                    alt=""
                  />{{ detailData.viewCount }}
                </p>
                <p v-else>
                  <img src="@/assets/images/solution/detail/eyes.png" alt="" />-
                </p>
                <p v-if="detailData.downloadCount != null">
                  <img
                    src="@/assets/images/solution/detail/left_down.png"
                    alt=""
                  />{{ detailData.downloadCount }}
                </p>
                <p v-else>
                  <img
                    src="@/assets/images/solution/detail/left_down.png"
                    alt=""
                  />-
                </p>
                <p v-if="detailData.collectCount != null">
                  <img
                    src="@/assets/images/solution/detail/star.png"
                    alt=""
                  />{{ detailData.collectCount }}
                </p>
                <p v-else>
                  <img src="@/assets/images/solution/detail/star.png" alt="" />-
                </p>
                <!--<p v-if="detailData.combineCount != null">
                  <img
                    src="@/assets/images/solution/detail/yinru.png"
                    alt=""
                  />{{ detailData.combineCount }}
                </p>
                <p v-else>
                  <img
                    src="@/assets/images/solution/detail/yinru.png"
                    alt=""
                  />-
                </p>-->
              </div>
              <div class="info_bottom">
                <p>联系人：{{ detailData.contact }}</p>
                <p>联系电话：{{ detailData.phone }}</p>
                <p>联系邮箱：{{ detailData.email }}</p>
              </div>

              <div class="info_bottom" style="display: flex; margin-left: 0">
                <p>能力类型 ： {{ detailData.abilityType }}</p>
                <p>能力提供方：{{ detailData.provider }}</p>
                <p>
                  首次上架：
                  {{
                    detailData.shelfTime
                      ? shelfTimeWith(detailData.shelfTime)
                      : detailData.createTime
                  }}
                </p>
              </div>
              <div class="info_bottom" style="display: flex; margin-left: 0">
                <p>
                  最新更新：
                  {{
                    detailData.editTime
                      ? shelfTimeWith(detailData.editTime)
                      : shelfTimeWith(detailData.shelfTime)
                  }}
                </p>
                <!-- <p>
                  能力分类：
                  <span v-if="detailData.ecologyType == 1">自有能力</span>
                  <span v-if="detailData.ecologyType == 2">生态能力</span>
                  <span
                    v-if="
                      detailData.ecologyType &&
                      detailData.ecologyType.split(',').length == 2
                    "
                    >自有能力+生态能力</span
                  >
                </p> -->
              </div>
              <div class="addCar">
                <button v-if="detailData.addCart" class="disabled">
                  已加入
                </button>
                <button v-else @click="add">加入预选</button>
                <button
                  v-if="showApply"
                  @click="apply"
                  style="margin-left: 16px"
                >
                  申请调度
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          @click="handleClick" :key="key"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>

      <div class="content" id="anchorContent">
        <div class="card applyCard" v-if="detailData.videoListShow">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="cards" style="display: flex; justify-content: center">
              <div
                class="item_card"
                style="
                  display: flex;
                  justify-content: center;
                  background: none;
                  border: none;
                  padding-bottom: 0;
                  box-shadow: none;
                "
                :style="videoData.style"
                v-for="(item, key) in detailData.videoList"
                :key="key + 1"
              >
                <video
                  id="video"
                  controls
                  :style="videoData.style"
                  :src="item.url"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="card applyCard"
          id="#sceneList"
          v-if="detailData.sceneListShow"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">应用场景</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
            <div class="cards">
              <div
                class="item_card"
                v-for="(item, key) in detailData.sceneList"
                :key="key + 1"
              >
                <img
                  v-if="item.cover"
                  v-lazy="`${item.cover}`"
                  alt=""
                  class="img"
                />
                <img
                  v-else
                  src="@/assets/images/ability/adlityDetail/apply.png"
                  alt=""
                  class="img"
                />
                <p class="title">{{ item.name }}</p>
                <p class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(item.description, 75)"
                      #title
                      >{{ item.description }}</template
                    >
                    {{ item.description }}
                  </a-tooltip>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="card applyCard"
          id="#functionList"
          v-if="detailData.functionListShow"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">核心功能</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
            <div class="flex function">
              <div
                class="card_list"
                v-for="(val, key) in detailData.functionList"
                :key="key"
              >
                <div class="card_title">
                  <img src="@/assets/images/solution/detail/card_icon.png" alt="" />
                  {{ val.name }}
                </div>
                <div class="margin_t_12 desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(val.description, 100)"
                      #title
                      >{{ val.description }}</template
                    >
                    {{ val.description }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 优势 -->
        <div
          class="card applyCard"
          id="#advantageList"
          v-if="detailData.advantageListShow"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">能力优势</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="flex function">
              <div
                class="card_list"
                v-for="(val, key) in detailData.advantageList"
                :key="key"
              >
                <div class="card_title">
                  <img src="@/assets/images/solution/detail/card_icon.png" alt="" />
                  能力优势 {{ key + 1 }}
                </div>
                <div class="margin_t_12 desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(val.advantage, 90)" #title>{{
                      val.advantage
                    }}</template>
                    {{ val.advantage }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 案例 -->
        <div
          class="card applyCard"
          id="#caseList"
          v-if="detailData.caseListShow"
        >
          <div
            class="card_content"
            style="padding-top: 0px; display: block; width: 100%"
          >
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">项目案例</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="cards">
              <div
                class="item_card"
                v-for="(item, key) in detailData.caseList"
                :key="key + 1"
              >
                <img
                  v-if="item.cover || item.cover !== null"
                  v-lazy="`${item.cover}`"
                  alt=""
                  class="img"
                />
                <img
                  v-else
                  alt=""
                  src="@/assets/images/solution/detail/case_default.png"
                  class="img"
                />
                <p class="title">{{ item.name }}</p>
                <p class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.intro, 64)" #title>{{
                      item.intro
                    }}</template>
                    {{ item.intro }}
                  </a-tooltip>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="card applyCard"
          id="#tariffList"
          v-if="detailData.tariffListShow"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">资费套餐</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="table">
              <a-row class="td">
                <a-col :span="8"></a-col>
                <a-col :span="8">套餐名称</a-col>
                <a-col :span="8">套餐资费</a-col>
              </a-row>
              <a-row
                class="tr"
                v-for="(val, key) in detailData.tariffList"
                :key="key"
              >
                <a-col :span="8" class="tariff_index col"
                  >套餐{{ key + 1 }}</a-col
                >
                <a-col :span="8" class="name col">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(val.setMeal, 30)" #title>{{
                      val.setMeal
                    }}</template>
                    {{ val.setMeal }}
                  </a-tooltip>
                </a-col>
                <a-col :span="8" class="price col">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(val.tariff, 30)" #title>{{
                      val.tariff
                    }}</template>
                    {{ val.tariff }}
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>

        <!-- <div class="card applyCard"  id="#team">
          <div
            class="tab_card"
            style="padding-top: 0px; display: block; width: 100%"
          >
          <div class="tab_content">
          <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px;height: 22px;"  alt="" />
          <div class="tit">支撑团队信息</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px;height: 22px;"  alt="" />
        </div>
                <div class="right_box" style="margin-left: 120px;margin-top: 60px;">
                  <p class="desc" style="line-height: 28px">
                   <p>服务时间：{{ detailData.serviceTime }}</p>
                   <p>联系人姓名：{{ detailData.contact }}</p>
                   <p>联系人电话：{{ detailData.phone }}</p>
                   <p>联系人邮箱：{{ detailData.email }}</p>
                  </p>
                </div>
          </div>
        </div> -->

        <!--生态-->
        <div
          class="tab_content"
          v-if="detailData.ecopartnerListShow"
          style="margin-bottom: 24px"
        >
          <img
            src="@/assets/images/solution/detail/leftIcon.png"
            style="width: 33px; height: 22px"
            alt=""
          />
          <div class="tit">生态合作</div>
          <img
            src="@/assets/images/solution/detail/rightIcon.png"
            style="width: 33px; height: 22px"
            alt=""
          />
        </div>
        <div
          class="provider_con"
          style="margin-bottom: 56px"
          id="#ecopartnerList"
          v-if="detailData.ecopartnerListShow"
        >
          <div>
            <div
              class="pro"
              v-for="(value, key) in detailData.ecopartnerNewList"
              :key="key"
              @click="goHtml(value)"
              :style="{
                cursor:
                  value.sync == 1 && value.auth == 1 ? 'pointer' : 'default',
              }"
            >
              <div
                class="score"
                v-if="value.sync == 1 && value.auth == 1 && value.type == 2"
              >
                <img class="scoreIcon" src="@/assets/images/score.png" alt="" />
                <div
                  class="scoreBody"
                  v-if="value.totalScore || value.introScore"
                >
                  <div class="scoreTitle">生态评分：</div>
                  <div class="scoreNum">
                    {{ value.totalScore || value.introScore }}
                  </div>
                </div>
                <div class="scoreBody" v-else>
                  <div
                    class="scoreNum"
                    style="font-size: 14px; margin-left: 10px"
                  >
                    暂无评分
                  </div>
                </div>
              </div>
              <div
                class="score"
                v-if="(value.sync != 1 || value.auth != 1) && value.type == 2"
              >
                <img
                  class="scoreIcon"
                  src="@/assets/images/scoreNo.png"
                  alt=""
                />
                <div class="scoreBody">
                  <div class="scoreNo">未认证</div>
                </div>
              </div>
              <a-tooltip>
                <template #title v-if="value.sync == 1 && value.auth == 1"
                  >点击查看详情</template
                >
                <div class="flex align-center" style="margin-bottom: 10px">
                  <img
                    style="width: 26px; height: 26px; margin-right: 8px"
                    src="@/assets/images/solution/detail/pro_icon.png"
                    alt=""
                    v-if="value.type == 2"
                  />
                  <img
                    style="width: 26px; height: 26px; margin-right: 8px"
                    src="@/assets/images/solution/detail/pro_own.png"
                    alt=""
                    v-else
                  />
                  <div
                    v-if="value.type == 2"
                    style="
                      font-weight: 400;
                      font-size: 16px;
                      line-height: 28px;
                      color: #2e3852;
                      margin-right: 10px;
                    "
                  >
                    生态合作方：{{ dealData(value.ecopartnerName) }}
                  </div>
                  <div
                    v-if="value.type == 1"
                    style="
                      font-weight: 400;
                      font-size: 16px;
                      line-height: 28px;
                      color: #2e3852;
                      margin-right: 10px;
                    "
                  >
                    自有能力方：{{ dealData(value.ecopartnerName) }}
                  </div>
                </div>
                <div class="flex align-center" style="margin-left: 34px">
                  <div
                    style="
                      width: 24%;
                      font-weight: 400;
                      font-size: 16px;
                      line-height: 28px;
                      color: #2e3852;
                    "
                  >
                    联系人：{{ dealData(value.children[0].ecologyContact) }}
                  </div>
                  <div
                    style="
                      width: 34%;
                      font-weight: 400;
                      font-size: 16px;
                      color: #2e3852;
                      line-height: 28px;
                    "
                  >
                    联系方式：{{ dealData(value.children[0].ecologyPhone) }}
                  </div>
                  <div
                    style="
                      width: 34%;
                      font-weight: 400;
                      font-size: 16px;
                      color: #2e3852;
                      line-height: 28px;
                    "
                  >
                    <div
                      style="
                        width: 340px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      "
                    >
                      <a-tooltip overlayClassName="tooltip_class">
                        <template
                          v-if="
                            isShowToolTip(
                              dealData(value.children[0].contactAddress),
                              14
                            )
                          "
                          #title
                          >{{ dealData(value.children[0].contactAddress) }}
                        </template>
                        负责区域：{{
                          dealData(value.children[0].contactAddress || "江苏省")
                        }}
                      </a-tooltip>
                    </div>
                  </div>
                </div>
              </a-tooltip>
            </div>
          </div>
        </div>

        <div
          class="tab_content"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
        >
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">能力附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list">
          <!-- <span v-if="item.category == 1">功能介绍：</span> -->
          <li v-for="(item, key) in detailData.fileList" :key="key">
            <div class="li_box" @click="fileShow(item)">
              <div class="left_box">
                <img
                  src="../../../assets/images/solution/detail/word.png"
                  alt=""
                  @click="doladFile"
                  style="width: 40px; height: 40px"
                />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <img
                src="../../../assets/images/solution/detail/download.png"
                alt=""
                @click.stop="downloadBtn(item)"
                style="cursor: pointer"
              />
            </div>
          </li>
        </ul>
      </div>

      <img
        class="top"
        src="../../../assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
      <view-list :id="viewId" :type="viewType"></view-list>
      <div class="bottom"></div>
    </div>
  </div>
  <a-modal
    v-model:visible="showDownloadModal"
    title="" :closable="false"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="450px"
  >
    <promptBox
      @downloadModalCancel="downloadModalCancel"
      @downloadModalConfirm="downloadModalConfirm"
      :msgObj="msgObj"
    />
  </a-modal>
  <a-modal
    v-model:visible="showDownloadForm"
    title="新增工单"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="600px"
  >
    <reviewForm
      @downloadFormCancel="downloadFormCancel"
      @downloadFormConfirm="downloadFormConfirm"
    />
  </a-modal>
  <!-- <DispatchTypeModal
    v-model="showDispatchModal"
    @select="handleDispatchSelect"
  /> -->
</template>
<script lang="ts" setup>
import viewList from "../../product/detail/viewList.vue";
import { onMounted, ref, toRaw, UnwrapRef, reactive, nextTick } from "vue";
import { getTradeList } from "../../../api/solutionNew/home";
import { useRouter, useRoute } from "vue-router";
import { addShoppingCart } from "../../../api/combine/shoppingCart.js";
import eventBus from "../../../utils/eventBus";
import { isShowToolTip } from "../../../utils/index.js";
import {
  dataDetailNew,
  getDownCount,
  getIntroduce,
  cancelCollect,
  collect,
  getReviewList,
} from "../../../api/moduleList/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import { getEcologicalDetails } from "@/api/login/login.js";
import axios from "axios";
import { message } from "ant-design-vue";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import { getAllUserList, roleDetail } from "@/api/system/user.js";
// import DispatchTypeModal from "@/components/DispatchTypeModal/index.vue";

interface FormState {
  name: string;
  code: string;
  categoryId: number | undefined;
  estimatedAmount: string;
  schemeId: number;
  status: string;
}

const route = useRoute();
const videoData = reactive({
  style: "position: fixed;right:200%;",
});
onMounted(() => {
  getData();
  setTimeout(() => {
    const videoDom = document.getElementById("video");
    if (videoDom.videoHeight > videoDom.videoWidth) {
      videoData.style = "height:400px;";
    } else {
      videoData.style = "width: 100%;";
    }
  }, 2000);
});
const speakList = ref();
const showDownloadModal = ref(false);
const showDownloadForm = ref(false);
const pageItemSize = ref("3");
const currentPage = ref("1");
const totalItemCount = ref(0);
const viewType = ref("2");
const loadShow = ref(false);
const reviewList = () => {
  let params = {
    sourceId: route.query.id,
    pageSize: pageItemSize.value,
    pageNo: currentPage.value,
    sourceType: "2",
    delStatus: 1,
  };
  getReviewList(params).then((res) => {
    if (res.code == 200) {
      totalItemCount.value = res.data.totalRows;
      speakList.value = res.data.rows.map((item) => ({
        ...item,
        show: item.creatorName === userInfo.realName,
      }));
    }
  });
};
reviewList();
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);
const ecoName = ref(null);
const msgObj = reactive({
  applyTimes:'',
  msg:'',
  fullPath:''
})

function change(v) {
  isActive.value = v;
}
const downloadBtn = (e) => {
  console.log("下载超限");
  //loadShow.value = true;

  getNewDownCount({
    businessId: route.query.id,
    businessType: 2,
  })
    .then((res) => {
      if (res.code == 200) {
        if (res.data) {
          const href = e.url;
          console.log(`oooooooooooooo`);

          const downName = e.name;
          let windowOrigin = window.location.origin;
					let token = localStorage.getItem("token");
					let newHref = href;
					if(href.includes(windowOrigin)){
					  newHref = "/portal" + href.split(windowOrigin)[1]
					}
				  window.open(windowOrigin + newHref + "?token=" + token);
        } else {
          if(res.msg.includes('5')){
            msgObj.applyTimes = 1
            msgObj.msg = res.msg
            msgObj.fullPath = route.fullPath
          }else{
            msgObj.applyTimes = 2
            msgObj.msg = res.msg
            msgObj.fullPath = route.fullPath
          }
          showDownloadModal.value = true;
        }
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

const fileShow = (val) => {
  loadShow.value = true;
  // 转PDF的下载
  pptTopdf({
    filePath: val.path,
    fileUrl: val.url,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
      if(res.data.includes(windowOrigin)){
      	newHref = "/portal" + res.data.split(windowOrigin)[1]
      }
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
          	windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
};
// const showDispatchModal = ref(false);

const formRef = ref();
const apply = () => {
  console.log("Opening dispatch modal");
  nextTick(() => {
    // showDispatchModal.value = true;
    handleDispatchSelect('售前调度');
  });
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const currentAnchor = ref("#desc");
const viewId = ref(route.query.id);
const back = () => {
  Router.back(-1);
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref({});

const transformEcoPartnerList = (originalList) => {
  // 边界情况处理
  if (!originalList || !Array.isArray(originalList)) {
    return [];
  }

  // 使用Map实现高性能分组
  const groupMap = originalList.reduce((map, item) => {
    // 跳过无效元素
    if (!item || typeof item !== "object") return map;

    try {
      // 安全提取字段（防止undefined报错）
      const {
        ecopartnerName,
        ecopartnerId,
        enterpriseId,
        totalScore,
        introScore,
        sync,
        auth,
        approve,
        delStatus,
      } = item;
      const key = `${ecopartnerName}|${ecopartnerId}|${enterpriseId}`;

      // 自动跳过缺失关键字段的元素
      if (!key.includes("undefined")) {
        if (!map.has(key)) {
          map.set(key, {
            ecopartnerName: ecopartnerId ? ecopartnerName : ecoName,
            ecopartnerId,
            enterpriseId,
            totalScore,
            introScore,
            sync,
            auth,
            type: ecopartnerId == null ? 1 : 2,
            approve,
            delStatus,
            children: [],
          });
        }
        // 安全提取子字段
        map.get(key).children.push({
          contactName: item.ecologyContact || item.contactName || "",
          contactPhone: item.ecologyPhone || item.contactPhone || "",
          contactAddress: item.contactAddress || "",
          ecologyContact: item.ecologyContact || item.contactName || "",
          ecologyPhone: item.ecologyPhone || item.contactPhone || "",
        });
      }
    } catch (e) {
      console.warn("Invalid data format:", e);
    }

    return map;
  }, new Map());

  return Array.from(groupMap.values());
};
const showApply = ref(false);
const getData = () => {
  if (route.query.id) {
    dataDetailNew(route.query.id)
      .then(async (res) => {
        roleDetail(res.data.contactId).then(val=>{
			    if(val.data.orgNamePath && val.data.orgNamePath.split("/").length == 3) {
			      if( val.data.orgNamePath.split("/")[1] == "苏移集成"){
			        ecoName.value = "江苏移动信息系统集成有限公司";
			      } else {
			        ecoName.value = val.data.orgNamePath.split("/")[1]
			      }
			    } else {
			      ecoName.value = val.data.orgName
			    }
          detailData.value = [];
          anchorList.value = [];
          if (
            (userInfo.orgId === 2 ||
            userInfo.orgId === 3 ||
            userInfo.orgId === 4 ||
            userInfo.orgId === 5 ||
            userInfo.orgId === 6 ||
            userInfo.orgId === 7 ||
            userInfo.orgId === 8 ||
            userInfo.orgId === 9 ||
            userInfo.orgId === 11 ||
            userInfo.orgId === 13 ||
            userInfo.orgId === 14 ||
            (userInfo.orgId !== 173 &&
              userInfo.orgId !== 184 &&
              userInfo.orgId !== 185 &&
              userInfo.orgId !== 211 &&
              userInfo.orgId !== 212 &&
              userInfo.orgId !== 213 &&
              userInfo.orgId !== 174 &&
              userInfo.orgId !== 186 &&
              userInfo.orgId !== 190 &&
              userInfo.orgId !== 191 &&
              userInfo.orgId !== 192) ||
            userInfo.orgId === 10 ||
            userInfo.orgId == 12 ) && userInfo.orgNamePath.split("/")[0] === "江苏公司"
          ) {
            showApply.value = true;
          }
          res.data.videoList = res.data.fileList.filter(
            (item) => item.category == 6 || item.type == 5
          );
          res.data.videoList.forEach(item => {
		      	item.url = window.location.origin + item.url + "?token=" + localStorage.getItem("token")
		      })
          res.data.fileList = res.data.fileList.filter(
            (item) => item.category != 6 && item.type != 5
          );
          let date = new Date(res.data.createTime);
          var Y = date.getFullYear();
          var M =
            date.getMonth() + 1 < 10
              ? "0" + (date.getMonth() + 1)
              : date.getMonth() + 1;
          var D =
            (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
          let GMT = Y + "-" + M + "-" + D;
          res.data.createTime = GMT;
          // 去除空数组对象
          res.data.advantageList = res.data.advantageList.filter(
            (item) => item.advantage !== ""
          );

          res.data.tariffList = res.data.tariffList.filter(
            (item) => item.setMeal !== "" || item.tariff !== ""
          );
          if (res.data.videoList.length != 0) {
            res.data.videoListShow = true;
            //anchorList.value.push({
            //  key: "videoList",
            //  href: "#videoList",
            //  title: "能力视频",
            //});
          }
          if (res.data.sceneList.length != 0) {
            res.data.sceneListShow = true;
            anchorList.value.push({
              key: "sceneList",
              href: "#sceneList",
              title: "应用场景",
            });
          }
          if (res.data.functionList.length != 0) {
            res.data.functionListShow = true;
            anchorList.value.push({
              key: "functionList",
              href: "#functionList",
              title: "核心功能",
            });
          }
          if (res.data.advantageList.length != 0) {
            res.data.advantageListShow = true;
            anchorList.value.push({
              key: "advantageList",
              href: "#advantageList",
              title: "能力优势",
            });
          }
          if (res.data.caseList.length != 0) {
            res.data.caseListShow = true;
            anchorList.value.push({
              key: "caseList",
              href: "#caseList",
              title: "项目案例",
            });
          }
          if (res.data.tariffList.length != 0) {
            res.data.tariffListShow = true;
            anchorList.value.push({
              key: "tariffList",
              href: "#tariffList",
              title: "资费套餐",
            });
          }
          // anchorList.value.push({
          //   key: "team",
          //   href: "#team",
          //   title: "支撑团队信息",
          // });
          if (
            (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) ||
            (res.data.enterpriseList && res.data.enterpriseList.length > 0)
          ) {
            res.data.ecopartnerListShow = true;
            anchorList.value = [
              ...anchorList.value,
              {
                key: "ecopartnerList",
                href: "#ecopartnerList",
                title: "生态合作",
              },
            ];
            if (false) {
              
            } else if (
              res.data.ecopartnerList &&
              res.data.ecopartnerList.length > 0
            ) {
              res.data.ecopartnerNewList = transformEcoPartnerList(
                res.data.ecopartnerList
              );
            }
          }
          console.log(res.data.ecopartnerNewList);
          if (res.data.fileList.length != 0) {
            anchorList.value.push({
              key: "download",
              href: "#download",
              title: "能力附件",
            });
          }
          if (res.data.fileList.length == 0) {
            res.data.fileList = false;
            detailData.value = res.data;
          }
          // res.data.provider = res.data.provider.split("/")[1];
          detailData.value = res.data;
        });
      })
      .catch((err) => {
        console.log(err);
      });
  }
};

const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "2",
  }).then((res) => {
    getData();
    eventBus.emit("cartRefresh");
  });
};
const tabList = ref([]);
const rules = {
  name: [
    {
      required: true,
      message: "请输入商机名称",
      trigger: "blur",
    },
  ],
  code: [
    {
      required: true,
      message: "请输入商机编号",
      trigger: "blur",
    },
  ],
  categoryId: [
    {
      required: true,
      message: "请选中所属行业",
      trigger: "blur",
    },
  ],
  estimatedAmount: [
    {
      required: true,
      message: "请输入预计金额",
      trigger: "blur",
    },
  ],
};
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();
const isShow = ref("desc");
const anchorList = ref([]);

const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
  collectActive.value = !collectActive.value;
};

const previewVisible = ref(false);
const getBtn = () => {
  previewVisible.value = true;
};
const summaryDesc = (rule, value) => {
  return new Promise((resolve, reject) => {
    // 简化字符串处理逻辑
    const trimmedValue = value ? value.trim() : "";
    if (trimmedValue.length > 50) {
      reject(new Error("长度不得超过五十"));
    } else {
      resolve();
    }
  });
};
const summaryDescMoney = (rule, value) => {
  return new Promise((resolve, reject) => {
    // 简化字符串处理逻辑
    const trimmedValue = value ? value.trim() : "";

    // 检查是否包含小数点，并据此判断是否可以以零开头
    const hasDecimalPoint = trimmedValue.includes(".");
    const startsWithZero = trimmedValue.startsWith("0");

    // 如果以零开头且不含小数点，则只允许输入为 "0"
    if (startsWithZero && !hasDecimalPoint && trimmedValue !== "0") {
      reject(new Error("金额不能以0开头"));
    } else {
      const numericValue = parseFloat(trimmedValue);
      if (numericValue <= 0 || numericValue > 100000000) {
        // 检查是否为非数字、小于等于0或大于100000000
        reject(new Error("请输入有效的金额（大于0且不超过100000000）"));
      } else {
        resolve(); // 所有验证通过
      }
    }
  });
};
const formState: UnwrapRef<FormState> = reactive({
  name: "",
  code: "",
  categoryId: undefined,
  estimatedAmount: "",
  schemeId: undefined,
  status: undefined,
});
const closeModal = () => {
  previewVisible.value = false;
  formRef.value.resetFields();
};
const onSubmit = (val) => {
  formRef.value
    .validate()
    .then(() => {
      formState.schemeId = Number(route.query.id.toString());
      formState.categoryId = Number(formState.categoryId);
      formState.status = val;
      getIntroduce(toRaw(formState)).then((res) => {
        if (res.code == 200) {
          formRef.value.resetFields();
          previewVisible.value = false;
          if (val == 1) {
            message.success("申请成功");
          } else {
            message.success("保存成功");
          }
        } else {
          message.error(res.msg);
        }
      });
    })
    .catch((err) => {
      console.log("error", err);
    });
};
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
const dealData = (value) => {
  if (value) {
    return value;
  } else {
    return "-";
  }
};
const shelfTimeWith = (value) => {
  if (value) {
    return value.slice(0, 10);
  }
  return "-";
};
const sceneKey = ref(1);
const advantageKey = ref(1);
const caseKey = ref(1);
const tariffKey = ref(1);
eventBus.on("moduleDetailRefresh", getData);
// 下载超限提示弹窗取消按钮
const downloadModalCancel = () => {
  showDownloadModal.value = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  showDownloadModal.value = false;
  showDownloadForm.value = true;
};
const downloadFormCancel = () => {
  showDownloadForm.value = false;
};
const downloadFormConfirm = () => {
  showDownloadForm.value = false;
};

const goHtml = (item) => {
  if (item.sync == 1 && item.auth == 1) {
    console.log(item.ecopartnerId);
    if (item.ecopartnerId > 10000) {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.ecopartnerId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    } else {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.enterpriseId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    }
  }
};

const handleDispatchSelect = (type) => {
  console.log("Selected type:", type);
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const dispatchConfig = {
    售前调度: {
      to: "starWork",
      checkPermission: () => true,
    },
    售中调度: {
      to: "coordination",
      checkPermission: (userInfo) =>
        userInfo.roleKeyList.includes("deliveryManager"),
      errorMessage: "目前售中调度工单仅支持交付经理发起",
    },
    售后调度: {
      to: "",
      checkPermission: () => false,
      errorMessage: "售后调度工单流程暂未开放，敬请期待！",
    },
  };
  const config = dispatchConfig[type];
  if (!config) return;

  if (!config.checkPermission(userInfo)) {
    message.warning(config.errorMessage);
    return;
  }

  const params = {
    id: route.query.id,
    action: "edit",
    from: "ability",
    to: config.to,
    active: '调度中心',
  };

  const searchParams = new URLSearchParams(params);
  window.location.href = window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString();
};
</script>
<style lang="scss" scoped>
@import "./index.scss";

.provider_con {
  background: linear-gradient(163deg, #f2f5f8 0%, #f6f7f9 38%, #ffffff 100%);
  box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
    -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #ffffff;
  padding: 32px 40px;
  .pro {
    margin-bottom: 30px;
    padding: 30px 20px 20px 20px;
    box-shadow: 8px 8px 12px 0px rgba(4, 58, 107, 0.04);
    background: linear-gradient(90deg, #ffffff 0%, #eaeff7 100%);
    position: relative;
    .score {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      min-width: 80px;
      height: 28px;
      padding-right: 20px;
      background-color: #ffffff;
      border-radius: 15px;
      box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
      .scoreIcon {
        width: 24px;
        height: 24px;
        margin: 2px 6px 2px 2px;
      }
      .scoreBody {
        display: flex;
        .scoreTitle {
          line-height: 28px;
          font-size: 14px;
          color: #00000073;
          font-weight: 500;
        }
        .scoreNo {
          color: #00000073;
          font-size: 14px;
          line-height: 28px;
        }
        .scoreNum {
          line-height: 28px;
          font-size: 18px;
          color: #ff9c39ff;
          font-weight: bold;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: #1a66fb;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>
