<template>
  <div id="zonePage">
    <div class="loading-overlay" v-if="viewLoading">
      <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">麒麟社区</span>
        <span class="title"> / </span>
        <span class="current">帖子详情</span>
      </div>
      <div @click="back" style="cursor: pointer; color: #2e7fff">返回</div>
    </div>

    <div style="margin-top: 50px">
      <div class="contentCard">
        <div class="top_card">
          <div class="card_content">
            <div class="flex align-center just-sb">
              <div class="text">帖子详情</div>
              <div>
                <div
                  style="display: inline-block"
                  v-if="
                    userInfo.roleKeyList.includes('auditManager') ||
                    userInfo.roleKeyList.includes('sysAdmin')
                  "
                >
                  <a-button
                    type="primary"
                    style="
                      background: linear-gradient(
                        270deg,
                        #0142fd 0%,
                        #2475f9 100%
                      );
                      border-radius: 4px;
                      font-weight: 500;
                      border: none;
                    "
                    v-if="forumData.top !== 1"
                    @click="toTop(1)"
                  >
                    置顶
                  </a-button>
                  <a-button
                    type="primary"
                    style="
                      background: linear-gradient(
                        270deg,
                        #0142fd 0%,
                        #2475f9 100%
                      );
                      border-radius: 4px;
                      font-weight: 500;
                      border: none;
                    "
                    v-else
                    @click="toTop(2)"
                  >
                    取消置顶
                  </a-button>
                </div>
                <a-button
                  v-if="userInfo.id == forumData.createBy"
                  type="primary"
                  style="
                    background: rgba(245, 29, 15, 0.1);
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    color: #f51d0f;
                    margin-left: 16px;
                  "
                  @click="showDelete"
                >
                  删除
                </a-button>
                <a-button
                  v-if="
                    forumData.status == '-1' &&
                    userInfo.id == forumData.createBy
                  "
                  type="primary"
                  @click="handleSubmit"
                  style="
                    background: linear-gradient(
                      270deg,
                      #0142fd 0%,
                      #2475f9 100%
                    );
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    margin-left: 16px;
                  "
                  :loading="addLoading"
                >
                  提交
                </a-button>
                <a-button
                  @click="trackView"
                  v-if="
                    (forumData.type == 1 &&
                      userInfo.id == forumData.createBy) ||
                    (forumData.type == 1 &&
                      userInfo.roleKeyList.includes('sysAdmin')) ||
                    (forumData.type == 1 &&
                      userInfo.roleKeyList.includes('auditManager'))
                  "
                  type="primary"
                  style="
                    background: linear-gradient(
                      270deg,
                      #0142fd 0%,
                      #2475f9 100%
                    );
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    margin-left: 16px;
                  "
                >
                  流程跟踪
                </a-button>
              </div>
            </div>
            <div
              class="margin_t_20"
              v-if="
                forumData.status == '-1' && userInfo.id == forumData.createBy
              "
              style="border: 1px solid #e8e8e8; border-radius: 4px"
            >
              <a-row class="form-row">
                <a-col :span="4" class="label-col">
                  <span class="warning" style="color: red;">*</span>
                  <div class="label-text">主题</div>
                </a-col>
                <a-col :span="20" class="input-col">
                  <a-input
                    v-model:value="forumData.title"
                    placeholder="请输入主题（20字以内）"
                    bordered="false"
                    :maxlength="20"
                  />
                </a-col>
              </a-row>

              <a-row class="form-row">
                <a-col :span="4" class="label-col">
                  <span class="warning" style="color: red;">*</span>
                  <div class="label-text">问题类型</div>
                </a-col>
                <a-col :span="20" class="input-col">
                  <div
                    class="margin_t_12"
                    style="margin-left: 11px; margin-top: 0"
                  >
                    {{ dealType(forumData.type) }}
                  </div>
                </a-col>
              </a-row>

              <a-row class="form-row">
                <a-col
                  :span="4"
                  class="label-col"
                  style="align-items: flex-start; padding-top: 4px"
                >
                  <span class="warning" style="color: red">*</span>
                  <div class="label-text">内容</div>
                </a-col>
                <a-col :span="20" class="input-col">
                  <a-textarea
                    v-model:value="forumData.content"
                    placeholder="请输入内容（500字以内）"
                    :rows="8"
                    bordered="false"
                    :maxlength="500"
                  />
                </a-col>
              </a-row>

              <a-row class="form-row">
                <a-col :span="4" class="label-col">
                  <div class="label-text">附件</div>
                </a-col>
                <a-col :span="20" class="input-col" style="align-items: center">
                  <a-upload
                    v-model:file-list="forumData.file"
                    :customRequest="uploadFile"
                    :multiple="false"
                    :max-count="1"
                    accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
                    :show-upload-list="false"
                  >
                    <a-button
                      v-if="ShowBtn"
                      style="
                        margin-left: 14px;
                        background: linear-gradient(
                          270deg,
                          #0142fd 0%,
                          #2475f9 100%
                        );
                        border-radius: 4px 4px 4px 4px;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 20px;
                        padding: 6px 16px;
                      "
                      :loading="uploading"
                    >
                      上传附件
                    </a-button>
                    <template v-if="!ShowBtn">
                      <div class="custom-upload-item">
                        <span
                          class="file-name"
                          v-if="hasFile"
                          style="color: #0c70eb"
                        >
                          {{ displayFileName }}
                        </span>
                        <delete-outlined
                          v-if="hasFile"
                          @click.stop="handleRemove"
                        />
                      </div>
                    </template>
                  </a-upload>
                  <div
                    v-if="!hasFile"
                    style="
                      font-size: 14px;
                      color: rgba(0, 0, 0, 0.25);
                      margin-left: 14px;
                    "
                  >
                    注：视频限制20M以内
                  </div>
                </a-col>
              </a-row>
              <a-row class="form-row">
                <a-col :span="4" class="label-col">
                  <span class="warning" style="color: red;">*</span>
                  <div class="label-text">是否匿名</div>
                </a-col>
                <a-col :span="20" class="input-col">
                  <a-radio-group
                    v-model:value="forumData.anonymity"
                    style="margin-left: 16px"
                  >
                    <a-radio :value="1">是</a-radio>
                    <a-radio :value="0">否</a-radio>
                  </a-radio-group>
                </a-col>
              </a-row>
            </div>
            <div class="margin_t_20" v-else>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_r_16 margin_t_12">主题</div>
                </div>
                <div class="table_right">
                  <div class="margin_t_12">{{ forumData.title }}</div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_r_16 margin_t_12">提交人</div>
                </div>
                <div
                  class="table_right"
                  style="flex: 2"
                  v-if="forumData.anonymity == 0"
                >
                  <div class="margin_t_12">
                    <span style="margin-right: 4px">
                      {{ forumData.createName }}
                    </span>
                    {{ forumData.orgPathName }}
                  </div>
                </div>
                <div
                  class="table_right flex"
                  style="align-items: center; justify-content: center"
                  v-if="forumData.anonymity == 1"
                >
                  <div>匿名</div>
                </div>
                <div
                  class="table_left flex"
                  style="align-items: center; justify-content: center"
                >
                  <div>提交类型</div>
                </div>
                <div
                  class="table_right flex"
                  style="align-items: center; justify-content: center"
                >
                  <div>{{ dealType(forumData.type) }}</div>
                </div>
                <div
                  class="table_left flex"
                  style="align-items: center; justify-content: center"
                >
                  <div>发布日期</div>
                </div>
                <div
                  class="table_right flex"
                  style="align-items: center; justify-content: center"
                >
                  <div>{{ forumData.createTime }}</div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_r_16 margin_t_12">内容</div>
                </div>
                <div class="table_right">
                  <div
                    class="margin_t_12 margin_b_12"
                    style="white-space: pre-wrap"
                  >
                    {{ forumData.content }}
                  </div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left table_line">
                  <div class="margin_r_16 margin_t_12">附件</div>
                </div>
                <div class="table_right table_line">
                  <div
                    class="margin_t_12 tableFile"
                    v-if="forumData.file && forumData.file.fileName"
                    @click="previewFile(forumData.file)"
                  >
                    {{ forumData.file.fileName }}
                  </div>
                  <div class="margin_t_12" v-else>暂无</div>
                </div>
              </div>
            </div>
            <idea-list
              @feed-back="feedBack"
              @allot-view="allotView"
              @unsolved="unsolved"
              @preview-file="previewFile"
              :dataDetail="forumData"
              v-if="forumData.type == 1"
            />
          </div>
        </div>

        <div class="leaveWord">
          <div class="leaveContent">
            <review-list :id="forumData.id" @refresh-page="refreshPage" />
          </div>
        </div>

        <div class="leaveWord">
          <div class="leaveContent">
            <attach-list :detailData="forumData" @refresh-page="refreshPage" />
          </div>
        </div>
      </div>
    </div>
    <!-- 分配 -->
    <a-modal
      :visible="allotVisible"
      @cancel="closeModal"
      width="1200px"
      :footer="null"
    >
      <allot-model
        @reject-view="rejectView"
        v-if="allotVisible"
        @modal-close="closeModal"
        @preview-file="previewFile"
        :dataDetail="forumData"
        @refresh="getData"
      />
    </a-modal>
    <!-- 情况反馈 -->
    <a-modal
      :visible="backVisible"
      @cancel="backModal"
      width="1200px"
      :footer="null"
    >
      <couple-list
        :typeObject="typeObject"
        :id="forumData.id"
        @decline-view="declinView"
        @cancel-idea="cancelIdea"
        @refresh-view="getData"
        v-if="backVisible"
      />
    </a-modal>
    <!-- 驳回 -->
    <a-modal
      :visible="refuseVisible"
      @cancel="closeRefuse"
      width="680px"
      :footer="null"
    >
      <refuse-model
        @cancel-view="closeRefuse"
        v-if="refuseVisible"
        :id="forumData.id"
      />
    </a-modal>
    <!-- 拒绝 -->
    <a-modal
      :visible="rejectVisible"
      @cancel="closeReject"
      width="680px"
      :footer="null"
    >
      <reject-model
        @close-view="closeView"
        :id="forumData.id"
        v-if="rejectVisible"
      />
    </a-modal>
    <!-- 流程跟踪 -->
    <a-modal
      :visible="trackVisible"
      @cancel="closeTrack"
      width="590px"
      :footer="null"
    >
      <track-list v-if="trackVisible" :history="forumData.processList" />
    </a-modal>
    <a-modal
      v-model:visible="deleteShow"
      :destroyOnClose="true"
      width="500px"
      @ok="deleteCard"
      @cancel="!deleteShow"
    >
      是否删除该帖？
    </a-modal>
    <a-modal
      :visible="previewVisible"
      centered
      :footer="null"
      @cancel="handleCancel"
    >
      <video
        v-if="isVideo"
        controls
        style="width: 100%; height: 100%; margin-top: 20px"
        :src="videoUrl"
      />
      <img
        v-if="isImage"
        style="width: 100%; height: 100%; margin-top: 20px"
        :src="imgUrl"
        alt=""
      />
    </a-modal>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  computed,
  provide,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import reviewList from "./components/reviewList.vue";
import attachList from "./components/attachList.vue";
import allotModel from "./components/allotModel.vue";
import ideaList from "./components/ideaList.vue";
import refuseModel from "./components/refuseModel.vue";
import rejectModel from "./components/rejectModel.vue";
import coupleList from "./components/coupleList.vue";
import trackList from "./components/trackList.vue";
import { DeleteOutlined } from "@ant-design/icons-vue";
import {
  getDetail,
  setTop,
  setDelete,
  refuseTop,
  setConfirm,
  rsetMessage,
} from "@/api/community/index.js";
import { message } from "ant-design-vue";
export default defineComponent({
  components: {
    reviewList,
    attachList,
    allotModel,
    ideaList,
    refuseModel,
    coupleList,
    trackList,
    rejectModel,
    DeleteOutlined,
  },
  setup() {
    const Route = useRoute();
    const Router = useRouter();
    const data = reactive({
      viewLoading: false,
      uploading: false,
      addLoading: false,
      ShowBtn: false,
      deleteShow: false,
      refuseVisible: false,
      previewVisible: false,
      isVideo: false,
      isImage: false,
      videoUrl: "",
      imgUrl: "",
      allotVisible: false,
      backVisible: false,
      rejectVisible: false,
      trackVisible: false,
      typeObject: {},
      forumData: {},
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      options: [
        { name: "需求建议", id: 1 },
        { name: "官方公告", id: 2 },
        // { name: "技术交流", id: 3 },
        // { name: "经验分享", id: 4 },
        // { name: "问答互勉", id: 5 },
      ],
      backedId: "",
    });
    const getData = () => {
      console.log(`刷新刷新`);
      getDetail(Route.query.id).then((res) => {
        data.forumData = res.data;
        data.backedId = res.data.file?.id;
        console.log(data.forumData, `ookokoko`);
        data.forumData.processList = res.data.processList.reverse();
        if (data.forumData.file.fileName == null) {
          data.ShowBtn = true;
        }
      });
    };
    const refreshPage = () => {
      console.log(`刷新刷新`);
      getData();
    };
    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          downloadImage(e, 1);
        } else if (isImage) {
          downloadImage(e, 2);
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };
    const downloadImage = (file, type) => {
      if (type == 1) {
        data.isImage = false;
        data.isVideo = true;
        const href = file.fileUrl;
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = href;
        if (href.includes(windowOrigin)) {
          newHref = "/portal" + href.split(windowOrigin)[1];
        }
        data.videoUrl = windowOrigin + newHref + "?token=" + token;
      } else if (type == 2) {
        data.isImage = true;
        data.isVideo = false;
        data.imgUrl = file.fileUrl;
      }
      data.previewVisible = true;
    };
    const handleCancel = () => {
      data.previewVisible = false;
    };
    const unsolved = (val) => {
      data.typeObject = val;
      data.backVisible = true;
    };

    const back = () => {
      Router.back(-1);
    };

    const allotView = () => {
      data.allotVisible = true;
    };

    const closeModal = () => {
      data.allotVisible = false;
    };

    const closeRefuse = () => {
      data.refuseVisible = false;
    };

    const backModal = () => {
      data.backVisible = false;
    };

    const rejectView = () => {
      data.refuseVisible = true;
      data.allotVisible = false;
    };

    const declinView = () => {
      data.rejectVisible = true;
      data.backVisible = false;
    };

    const feedBack = (val) => {
      data.typeObject = val;
      data.backVisible = true;
    };

    const closeReject = () => {
      data.rejectVisible = false;
    };

    const cancelIdea = () => {
      data.backVisible = false;
    };

    const closeView = () => {
      data.rejectVisible = false;
    };
    const closeTrack = () => {
      data.trackVisible = false;
    };

    const trackView = () => {
      data.trackVisible = true;
    };
    onMounted(() => {
      getData();
    });
    const dealType = (v) => {
      switch (v) {
        case 1:
          return "需求建议";
        case 2:
          return "官方公告";
        // case 3:
        //   return "技术交流";
        // case 4:
        //   return "经验分享";
        // case 5:
        //   return "问答互勉";
        default:
          return v;
      }
    };
    // 置顶
    const toTop = async (v) => {
      try {
        const response =
          v === 1
            ? await setTop(data.forumData.id)
            : await refuseTop(data.forumData.id);

        if (response.code === 200) {
          const action = v === 1 ? "置顶" : "取消置顶";
          message.success(`${action}成功`);
          data.forumData.top = v === 1 ? "1" : null;
        }
      } catch (error) {
        console.error("操作失败:", error);
        message.error("操作失败，请重试");
      } finally {
        await getData();
      }
    };
    const deleteCard = () => {
      setDelete(data.forumData.id).then((res) => {
        if (res.code == 200) {
          message.warning("删除成功");
          Router.back(-1);
        }
      });
    };
    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");
      const maxVideoSize = 20 * 1024 * 1024;

      if (isVideo && file.size > maxVideoSize) {
        message.error("视频文件大小不能超过20MB");
        info.onError();
        return;
      }
      data.uploading = true;
      data.ShowBtn = true;
      try {
        let formData = new FormData();
        formData.append("file", info.file);
        const result = await uploadFileList(formData);
        if (result.code === 200) {
          if (!data.forumData.file || Array.isArray(data.forumData.file)) {
            data.forumData.file = {};
          }
          data.forumData.file = {
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
          };
          console.log(data.forumData, `上传后的 forumData`);
          message.success("上传成功");
          info.onSuccess();
          return result;
        } else {
          message.error("上传失败");
          data.ShowBtn = true;
        }
      } catch (error) {
        console.error("上传出错:", error);
      } finally {
        data.uploading = false;
        data.ShowBtn = false;
      }
    };
    const normalizedFileList = computed(() => {
      if (!data.forumData.file) return [];
      return Array.isArray(data.forumData.file)
        ? data.forumData.file
        : [data.forumData.file];
    });

    const hasFile = computed(() => {
      return (
        data.forumData.file &&
        (Array.isArray(data.forumData.file)
          ? data.forumData.file.length > 0
          : Object.keys(data.forumData.file).length > 0)
      );
    });

    const displayFileName = computed(() => {
      if (!data.forumData.file) return "";
      if (Array.isArray(data.forumData.file)) {
        return data.forumData.file[0]?.fileName || "";
      }
      return data.forumData.file.fileName || "";
    });

    const handleRemove = () => {
      data.forumData.file = Array.isArray(data.forumData.file) ? [] : {};
      data.ShowBtn = true;
    };
    const handleSubmit = () => {
      if (data.uploading == true) {
        message.error("附件上传中");
        return;
      }
      data.addLoading = true;
      if (!data.forumData.title) {
        message.error("请填写主题！");
        data.addLoading = false;
        return;
      }

      if (!data.forumData.content) {
        message.error("请填写内容！");
        data.addLoading = false;
        return;
      }

      if (data.forumData.anonymity === undefined) {
        message.error("请选择是否匿名发布！");
        data.addLoading = false;
        return;
      }
      const submitData = {
        id: data.forumData.id,
        title: data.forumData.title,
        type: data.forumData.type,
        content: data.forumData.content,
        anonymity: data.forumData.anonymity,
        ...(data.forumData.file?.fileName && {
          file: {
            id: data.backedId,
            fileName: data.forumData.file.fileName,
            filePath: data.forumData.file.filePath,
            fileUrl: data.forumData.file.fileUrl,
          },
        }),
      };

      console.log(submitData, `提交数据`, data.forumData);

      rsetMessage(submitData).then((res) => {
        if (res.code == 200) {
          message.success("发帖成功");
          getData();
          Router.back(-1);
          data.addLoading = false;
        } else {
          data.addLoading = false;
        }
      });
    };
    const showDelete = () => {
      data.deleteShow = true;
    };
    return {
      ...toRefs(data),
      showDelete,
      previewFile,
      handleRemove,
      refreshPage,
      handleSubmit,
      deleteCard,
      normalizedFileList,
      hasFile,
      displayFileName,
      uploadFile,
      toTop,
      dealType,
      allotView,
      trackView,
      handleCancel,
      closeView,
      closeTrack,
      feedBack,
      cancelIdea,
      unsolved,
      closeReject,
      backModal,
      declinView,
      closeRefuse,
      rejectView,
      closeModal,
      back,
    };
  },
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>