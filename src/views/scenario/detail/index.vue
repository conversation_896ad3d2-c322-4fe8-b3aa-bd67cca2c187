<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" :tip="loadShowTitle"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">场景</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <div v-if="isMarket">
          <div v-if="!showSelect" @click="buy">订购</div>
          <div v-else @click="bornList">生成清单</div>
        </div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <p>{{ detailData.name }}</p>
            </div>
            <div class="left_middle">
              <p class="info">
                {{ detailData.introduce }}
              <!-- <div class="flex">
                <div style="width: 112px;">需求方案组合： </div>
                <div class="proList">{{ detailData.list }}</div>
              </div> -->
              </p>
              <div class="info_bottom">
                <div class="label flex" style="display: inline-block; margin-right: 0">
                  <!-- <span
                    style="margin-right: 6px"
                    v-for="(item, key) in detailData.typeName"
                    :key="key"
                    >{{ item }}</span
                  > -->
                </div>
                <div style="margin-left: 0; display: inline-block" class="tips">
                  <p v-if="detailData.viewCount != null">
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />
                    {{ detailData.viewCount }}
                  </p>
                  <p v-else>
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />
                    -
                  </p>
                </div>
              </div>
              <div class="info_bottom">
                <p style="margin-bottom: 0;">场景分类：{{ dealClassify() }}<span v-if="detailData?.customers && detailData?.customers != ''" style="margin-left: 30px;">目标客户：{{ detailData.customers }}</span></p>
                <p style="display: block;">应用市场：{{ detailData.markets }}</p>
              </div>
              <div style="display: block; margin-left: 0; margin-bottom: 8px"></div>
            </div>
            <div class="left_bottom" v-if="!isMarket">
              <div class="addCar">
                <button v-if="detailData.addOrder" class="disabled">
                  已加入
                </button>
                <button v-else @click="toBuy">加入预选</button>
              </div>
              <div class="addCar" style="margin-left: 20px">
                <button @click="toDiy">一键定制</button>
              </div>
            </div>
          </div>
          <div class="right">
            <img v-lazy="detailData.mainImg" alt="" style="width: 100%; height: 100%" />
          </div>
        </div>
      </div>

      <div class="anchors" v-if="!isMarket">
        <a-anchor direction="horizontal" :affix="false" v-for="(item, key) in anchorList" @click="handleClick">
          <a-anchor-link :class="{ currentActive: isActive === key }" @click="change(key)" :href="item.href"
            :title="item.title" />
        </a-anchor>
      </div>

      <div class="content" id="anchorContent">
        <div v-if="isMarket" style="height: 45px"></div>

        <div class="card applyCard" style="padding-bottom: 36px" id="#desc" v-if="isMarket">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content" style="margin-bottom: 24px">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">需求方案</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="cards_c">
              <div class="card_list">
                <el-checkbox style="display: inline-block; margin-right: 10px" v-if="showSelect"
                  :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
                <div class="card_title" style="display: inline-block">
                  安全消防
                </div>
                <div class="card_desc">
                  场所安全消防管理是企业安全生产的重要组成部分，直接关系到员工及消费者的生命财产安全和企业稳定运营。
                </div>
                <div class="label_box">
                  <span>防止安全隐患</span><span>防偷防盗</span><span>防止消费纠纷</span>
                </div>
                <div class="line"></div>
                <div class="tuijian">产品推荐</div>
                <div class="list">
                  <div class="con_card" v-for="(item, index) in firstList" :key="index">
                    <a-checkbox-group :value="movalue" v-if="showSelect">
                      <a-checkbox :value="item.id" @change="getModuId">
                      </a-checkbox>
                    </a-checkbox-group>
                    <img v-lazy="item.img" alt="" />
                    <div class="con_right">
                      <div class="con_top">
                        <span class="name">{{ item.name }}</span>
                        <div class="con_con" v-if="!showSelect">
                          <a-tooltip overlayClassName="tooltip_class">
                            <template v-if="isShowToolTip(
      item.productList[0].specification,
      19
    )
      " #title>
                              {{ item.productList[0].specification }}</template>
                            {{ item.productList[0].specification }}
                          </a-tooltip>
                        </div>
                        <a-select v-else v-model:value="item.selectType" placeholder="请选择" @change="changeTypePhone">
                          <a-select-option v-for="value in item.productList" :key="value.id" :value="value.id">
                            <a-tooltip placement="topLeft">
                              <template v-if="isShowToolTip(value.specification, 10)" #title>{{ value.specification
                                }}</template>
                              {{ value.specification }}
                            </a-tooltip>
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card_list">
                <el-checkbox style="display: inline-block; margin-right: 10px" v-if="showSelect"
                  :indeterminate="isIndeterminate1" v-model="checkAll1" @change="handleCheckAllChange1"></el-checkbox>
                <div class="card_title" style="display: inline-block">
                  网络环境
                </div>
                <div class="card_desc">
                  改善整体卖场网络环境，提升员工工作效率、促进员工健康与满意度，同时给消费者提供一个舒适、整洁、高效的购物环境。
                </div>
                <div class="label_box">
                  <span>优化网络环境</span><span>提升购物体验</span>
                </div>
                <div class="line"></div>
                <div class="tuijian">产品推荐</div>
                <div class="list">
                  <div class="con_card" v-for="(item, index) in phoneList" :key="index">
                    <a-checkbox-group :value="movalue1" v-if="showSelect">
                      <a-checkbox :value="item.id" @change="getModuId1">
                      </a-checkbox>
                    </a-checkbox-group>
                    <img v-lazy="item.img" alt="" />
                    <div class="con_right">
                      <div class="con_top">
                        <span class="name">{{ item.name }}</span>
                        <div class="con_con" v-if="!showSelect">
                          <a-tooltip overlayClassName="tooltip_class">
                            <template v-if="isShowToolTip(
      item.productList[0].specification,
      19
    )
      " #title>
                              {{ item.productList[0].specification }}</template>
                            {{ item.productList[0].specification }}
                          </a-tooltip>
                        </div>
                        <a-select v-else v-model:value="item.selectType" placeholder="请选择" @change="changeTypePhone">
                          <a-select-option v-for="value in item.productList" :key="value.id" :value="value.id">
                            <a-tooltip placement="topLeft">
                              <template v-if="isShowToolTip(value.specification, 10)" #title>{{ value.specification
                                }}</template>
                              {{ value.specification }}
                            </a-tooltip>
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card_list">
                <el-checkbox style="display: inline-block; margin-right: 10px" v-if="showSelect"
                  :indeterminate="isIndeterminate2" v-model="checkAll2" @change="handleCheckAllChange2"></el-checkbox>
                <div class="card_title" style="display: inline-block">
                  体验提升
                </div>
                <div class="card_desc">
                  提供显示设备，展示卖场营销产品及电子化宣传材料，增强卖场内商品展示力度；提供收银系统，缩减用户付款结账时间，提升消费者购物体验。
                </div>
                <div class="label_box">
                  <span>优化购物流程</span><span>商品营销宣传</span><span>优化商品展示</span>
                </div>
                <div class="line"></div>
                <div class="tuijian">产品推荐</div>
                <div class="list">
                  <div class="con_card" v-for="(item, index) in moreList" :key="index">
                    <a-checkbox-group :value="movalue2" v-if="showSelect">
                      <a-checkbox :value="item.id" @change="getModuId2">
                      </a-checkbox>
                    </a-checkbox-group>
                    <img v-lazy="item.img" alt="" />
                    <div class="con_right">
                      <div class="con_top">
                        <span class="name">{{ item.name }}</span>
                        <div class="con_con" v-if="!showSelect">
                          <a-tooltip overlayClassName="tooltip_class">
                            <template v-if="isShowToolTip(
      item.productList[0].specification,
      19
    )
      " #title>
                              {{ item.productList[0].specification }}</template>
                            {{ item.productList[0].specification }}
                          </a-tooltip>
                        </div>
                        <a-select v-else v-model:value="item.selectType" placeholder="请选择" @change="changeTypePhone">
                          <a-select-option v-for="value in item.productList" :key="value.id" :value="value.id">
                            <a-tooltip placement="topLeft">
                              <template v-if="isShowToolTip(value.specification, 10)" #title>{{ value.specification
                                }}</template>
                              {{ value.specification }}
                            </a-tooltip>
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card_list">
                <el-checkbox style="display: inline-block; margin-right: 10px" v-if="showSelect"
                  :indeterminate="isIndeterminate3" v-model="checkAll3" @change="handleCheckAllChange3"></el-checkbox>
                <div class="card_title" style="display: inline-block">
                  管理提效
                </div>
                <div class="card_desc">
                  提供设备服务于管理流程的各个环节，提高员工工作效率，激发员工的服务意识，促进企业的持续发展。
                </div>
                <div class="label_box">
                  <span>提升办公效率</span><span>提升服务水平</span>
                </div>
                <div class="line"></div>
                <div class="tuijian">产品推荐</div>
                <div class="list">
                  <div class="con_card" v-for="(item, index) in guanList" :key="index">
                    <a-checkbox-group :value="movalue3" v-if="showSelect">
                      <a-checkbox :value="item.id" @change="getModuId3">
                      </a-checkbox>
                    </a-checkbox-group>
                    <img v-lazy="item.img" alt="" />
                    <div class="con_right">
                      <div class="con_top">
                        <span class="name">{{ item.name }}</span>
                        <div class="con_con" v-if="!showSelect">
                          <a-tooltip overlayClassName="tooltip_class">
                            <template v-if="isShowToolTip(
      item.productList[0].specification,
      19
    )
      " #title>
                              {{ item.productList[0].specification }}</template>
                            {{ item.productList[0].specification }}
                          </a-tooltip>
                        </div>
                        <a-select v-else v-model:value="item.selectType" placeholder="请选择" @change="changeTypePhone">
                          <a-select-option v-for="value in item.productList" :key="value.id" :value="value.id">
                            <a-tooltip placement="topLeft">
                              <template v-if="isShowToolTip(value.specification, 10)" #title>{{ value.specification
                                }}</template>
                              {{ value.specification }}
                            </a-tooltip>
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card_list">
                <el-checkbox style="display: inline-block; margin-right: 10px" v-if="showSelect"
                  :indeterminate="isIndeterminate4" v-model="checkAll4" @change="handleCheckAllChange4"></el-checkbox>
                <div class="card_title" style="display: inline-block">
                  宣传营销
                </div>
                <div class="card_desc">
                  卖场的市场经营竞争激烈，需要塑造独特的品牌形象和制定有效的营销策略来吸引顾客。
                </div>
                <div class="label_box">
                  <span>店铺宣传</span><span>线上营销</span><span>用户触达</span>
                </div>
                <div class="line"></div>
                <div class="tuijian">产品推荐</div>
                <div class="list">
                  <div class="con_card" v-for="(item, index) in tellList" :key="index">
                    <a-checkbox-group :value="movalue4" v-if="showSelect">
                      <a-checkbox :value="item.id" @change="getModuId4">
                      </a-checkbox>
                    </a-checkbox-group>
                    <img v-lazy="item.img" alt="" />
                    <div class="con_right">
                      <div class="con_top">
                        <span class="name">{{ item.name }}</span>
                        <div class="con_con" v-if="!showSelect">
                          <a-tooltip overlayClassName="tooltip_class">
                            <template v-if="isShowToolTip(
      item.productList[0].specification,
      19
    )
      " #title>
                              {{ item.productList[0].specification }}</template>
                            {{ item.productList[0].specification }}
                          </a-tooltip>
                        </div>
                        <a-select v-else v-model:value="item.selectType" placeholder="请选择" @change="changeTypePhone">
                          <a-select-option v-for="value in item.productList" :key="value.id" :value="value.id">
                            <a-tooltip placement="topLeft">
                              <template v-if="isShowToolTip(value.specification, 10)" #title>{{ value.specification
                                }}</template>
                              {{ value.specification }}
                            </a-tooltip>
                          </a-select-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="card applyCard" id="#productList" v-if="false && !isMarket && programList?.length > 0">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <!-- <div class="tit">产品清单</div> -->
              <div class="tit">需求方案</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="reqProgram">
              <div class="reqProgram-box" v-for="(item, index) in programList" :key="index">
                <div class="reqProgram-box-title">{{ item.name }}</div>
                <div style="padding-left: 20px;">
                  <div class="reqProgram-box-description">{{ item.description }}</div>
                  <div class="taglist">
                    <span v-for="(tagItem, index) in item.pointEntityList" :key="index" class="tag">
                      {{ tagItem.description }}
                    </span>
                  </div>
                  <div class="line"></div>
                  <div class="reqProgram-box-title">产品清单</div>
                  <div class="reqProgram-box-suggestList">
                    <div class="reqProgram-box-suggestList-content" v-for="(proItem, index) in item.demandProductList"
                      :key="index" @click="jumpTo(proItem)">
                      <img :src="proItem.image">
                      <div class="name">{{ proItem.name }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


        <div class="card applyCard" style="padding-bottom: 36px" id="#desc">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content" style="margin-bottom: 24px">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">产品推荐</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <a-tabs v-if="programList?.length > 0 && detailData?.sceneProductList?.length > 0" v-model:value="tabsActiveKey" @change="tabsChange" class="productClassTabs">
              <a-tab-pane key="1" tab="需求方案" force-render></a-tab-pane>
              <a-tab-pane key="2" tab="产品分类" force-render></a-tab-pane>
            </a-tabs>
            <div class="reqProgram" v-if="!isMarket && programList?.length > 0 && tabsActiveKey == 1">
              <div class="reqProgram-box" v-for="(item, index) in programList" :key="index">
                <div class="reqProgram-box-title">{{ item.name }}</div>
                <div style="padding-left: 20px;">
                  <div class="reqProgram-box-description">{{ item.description }}</div>
                  <div class="taglist">
                    <span v-for="(tagItem, index) in item.pointEntityList" :key="index" class="tag">
                      {{ tagItem.description }}
                    </span>
                  </div>
                  <div class="line"></div>
                  <div class="reqProgram-box-title">产品清单</div>
                  <div class="reqProgram-box-suggestList">
                    <div class="reqProgram-box-suggestList-content" v-for="(proItem, index) in item.demandProductList"
                      :key="index" @click="jumpTo(proItem)">
                      <img :src="proItem.image">
                      <div class="name">{{ proItem.name }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="reqProgram" v-if="!isMarket && detailData?.sceneProductList?.length > 0 && tabsActiveKey == 2">
              <div
                class="reqProgram-box"
                v-for="(item, index) in detailData.sceneProductList"
                :key="index"
              >
                <div class="reqProgram-box-title">{{ item.classifyName }}</div>
                <div style="padding-left: 20px">
                  <div class="reqProgram-box-description">
                    {{ item.description }}
                  </div>
                  <div class="line"></div>
                  <div class="reqProgram-box-title">产品清单</div>
                  <div class="reqProgram-box-suggestList">
                    <div
                      class="reqProgram-box-suggestList-content"
                      v-for="(proItem, index) in item.productLists"
                      :key="index"
                      @click="jumpTo(proItem)"
                    >
                      <img :src="proItem.productPic" />
                      <div class="name">{{ proItem.productName }}</div>
                      <img class="mainlyPromote" v-if="proItem.mainlyPromote == 1" src="@/assets/images/scenario/recommendPro.png"/>
                      <!-- <div class="mainlyPromote" v-if="proItem.mainlyPromote == 1">首推产品</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


        <div class="card applyCard" id="#sceneCase" v-if="detailData.caseList && detailData.caseList.length > 0 && !isMarket
      ">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">场景案例</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="cards" v-for="(item, key) in detailData.caseList" :key="key + 1">
              <img v-if="item.sceneImage == '' || item.sceneImage == undefined"
                src="../../../assets/images/ability/adlityDetail/bac.png" alt="" />
              <img v-else v-lazy="`${item.sceneImage}`" alt="" />
              <div class="right">
                <div style="height:40px;font-size: 18px; font-weight: 600; line-height: 40px">
                  {{ item.sceneCaseName }}
                </div>
                <div class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.sceneCase, 300)" #title>
                      {{ item.sceneCase }}</template>
                    {{ item.sceneCase }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card applyCard" id="#marketWords" v-if="!isMarket">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">营销话术</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="function">
              <div class="cardAll_list">
                <div class="top_key">
                  <img src="@/assets/images/scenario/label.png" alt="">
                  <span>营销话术</span>
                </div>
                <div class="flex">
                  <img src="@/assets/images/scenario/top.png" alt="">
                  <div class="desc lang">
                    <a-tooltip overlayClassName="tooltip_class">
                      <template v-if="isShowToolTip(detailData.language, 215)" #title>
                        {{ detailData.language }}</template>
                      {{ detailData.language }}
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 附件 -->
        <div class="tab_content" id="#download" v-if="detailData?.fileList?.length > 0">
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">产品附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list-file">
          <li v-for="(item, key) in detailData.fileList">
            <div class="li_box" @click="fileShow(item)">
              <div class="left_box">
                <img src="@/assets/images/solution/detail/word.png" alt="" style="width: 40px; height: 40px" />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <div class="flex" style="align-items: center;">
                <!-- <div class="flex linkIcon" @click.stop="shareLink(item)">
                  <LinkOutlined style="font-size: 21px;color: #2E7FFF;"/>
                </div> -->
                <img
                  src="@/assets/images/solution/detail/download.png"
                  alt=""
                  @click.stop="downloadBtn(item.url)"
                  style="cursor: pointer"
                />
              </div>
              <!-- <img src="@/assets/images/solution/detail/download.png" alt="" @click.stop="downloadBtn(item.url)"
                style="cursor: pointer" /> -->
            </div>
          </li>
        </ul>
        <view-list v-if="isMarket" :id="viewId" type="3"></view-list>
      </div>

      <img class="top" src="../../../assets/images/solution/detail/toTap.png" alt="" @click="scrollUp" />
      <div class="bottom"></div>
    </div>
    <view-list v-if="!isMarket" :id="viewId" type="3"></view-list>

    <!-- 引入弹框 -->
    <a-modal :visible="previewVisible" title="引入申请" @cancel="closeModal" :footer="null" width="600px">
      <a-form :model="formState" id="form" :rules="rules" ref="formRef">
        <a-row>
          <a-col :span="12">
            <a-form-item label="方案名称" name="projectName">
              <div>{{ dataDeal(detailData.name) }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="方案编号" name="contractAmount">
              <div>{{ dataDeal(detailData.code) }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="方案发布单位" name="contractAmount">
              <div>{{ dataDeal(detailData.provider) }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系人" name="contacts">
              <div>{{ dataDeal(detailData.contact) }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话" name="telephone">
              <div>{{ dataDeal(detailData.phone) }}</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系邮箱" name="email">
              {{ dataDeal(detailData.email) }}
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="商机名称" name="name" :rules="[
      { required: true, message: '请输入商机名称' },
      { validator: summaryDesc, trigger: 'blur' },
    ]">
          <a-input v-model:value="formState.name" placeholder="请输入商机名称" />
        </a-form-item>
        <a-form-item label="商机编号" name="code" :rules="[
      { required: true, message: '请输入商机编号' },
      { validator: summaryDesc, trigger: 'blur' },
    ]">
          <a-input v-model:value="formState.code" placeholder="请输入商机编号" />
        </a-form-item>
        <a-form-item label="所属行业" name="categoryId">
          <a-select v-model:value="formState.categoryId" placeholder="请选择所属行业"
            :rules="[{ required: true, message: '请选择所属行业' }]">
            <a-select-option v-for="item in tabList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="预估金额" name="estimatedAmount" :rules="[
      { required: true, message: '请输入预估金额' },
      { validator: summaryDescMoney, trigger: 'blur' },
          ]">
          <a-input v-model:value="formState.estimatedAmount" placeholder="请输入预估金额（万元）" type="number" />
        </a-form-item>
        <a-form-item style="text-align: center">
          <a-button style="margin-right: 20px" @click="closeModal">取消</a-button>
          <a-button type="primary" style="margin-right: 20px" @click="onSubmit(0)">保存</a-button>
          <a-button type="primary" @click="onSubmit(1)">我要申请</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>

  <a-modal v-model:visible="data.showDownloadModal" title="" :closable="false" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="450px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm" :msgObj="msgObj"/>
  </a-modal>
  <a-modal v-model:visible="data.showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <a-modal v-model:visible="shareVisable" title="分享链接" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="500px" @cancel="shareVisableClose">
    <div class="shareBody">
      <div class="shareContent">
        <LinkOutlined style="font-size: 26px;"/>
        <div class="shareContent-link">
          <div :title="`${shareTitle}方案`">{{shareTitle}}方案</div>
          <div :title="link">{{ link }}</div>
        </div>
      </div>
      <div class="copyLink">
        <div @click="copy">复制链接</div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { onMounted, ref, computed, toRaw, reactive } from "vue";
import { Base64 } from "js-base64";
import { useRouter, useRoute } from "vue-router";
import { Anchor } from "ant-design-vue";
import { addShop, addShopPro, toShopList } from "../../../api/buyList/index.js";
import viewList from "../../product/detail/viewList.vue";
import {
  getSceneDetail,
  getDownCount,
  getIntroduce,
  cancelCollect,
  collect,
  detail,
} from "../../../api/scenario/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import { getTradeList } from "../../../api/solutionNew/home";
import { getMakeUrl } from "../../../utils/getUrl";
import { isShowToolTip } from "../../../utils/index";
import {
  addShoppingCart,
  clearShopping,
} from "../../../api/combine/shoppingCart.js";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import axios from "axios";
import { message } from "ant-design-vue";
import eventBus from "../../../utils/eventBus";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
import { LinkOutlined } from '@ant-design/icons-vue';
import clipboard3 from 'vue-clipboard3'

const baseURL = getMakeUrl();
const route = useRoute();
onMounted(() => {
  getData();
});
const shareTitle = ref('')
const shareVisable = ref(false)
const link = ref('')
const isMarket = ref(false);
const movalue = ref([]);
const isIndeterminate = ref(false);
const checkAll = ref(false);

const programList = ref([]);

const getProgramDetail = () => {};
// getProgramDetail()
const handleCheckAllChange = () => {
  movalue.value =
    movalue.value.length === firstList.value.length
      ? []
      : [...firstList.value].map((item) => item.id);
};
const movalue1 = ref([]);
const isIndeterminate1 = ref(false);
const checkAll1 = ref(false);
const handleCheckAllChange1 = () => {
  movalue1.value =
    movalue1.value.length === phoneList.value.length
      ? []
      : [...phoneList.value].map((item) => item.id);
};
const movalue2 = ref([]);
const isIndeterminate2 = ref(false);
const checkAll2 = ref(false);
const handleCheckAllChange2 = () => {
  movalue2.value =
    movalue2.value.length === moreList.value.length
      ? []
      : [...moreList.value].map((item) => item.id);
};
const movalue3 = ref([]);
const isIndeterminate3 = ref(false);
const checkAll3 = ref(false);
const handleCheckAllChange3 = () => {
  movalue3.value =
    movalue3.value.length === guanList.value.length
      ? []
      : [...guanList.value].map((item) => item.id);
  console.log(movalue3.value);
};
const movalue4 = ref([]);
const isIndeterminate4 = ref(false);
const checkAll4 = ref(false);
const handleCheckAllChange4 = () => {
  movalue4.value =
    movalue4.value.length === tellList.value.length
      ? []
      : [...tellList.value].map((item) => item.id);
};
const showSelect = ref(false);
const isActive = ref(0);
const viewId = ref(route.query.id);
const loadShow = ref(false);
const loadShowTitle = ref('附件加载中')
const firstList = ref([
  {
    name: "安防监控",
    price: "8888",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/10/b3034c40-54da-4eed-a5df-561bab4cd2b6.jpg",
    id: "78",
    selectId: "630",
    selectType: "根据客户需求现场评估工作量，按工作量报价收费交付",
    productList: [
      {
        id: 630,
        productId: 78,
        name: "安防套餐",
        tariff: "8888",
        unit: "元",
        specification: "根据客户需求现场评估工作量，按工作量报价收费交付",
      },
    ],
  },
  {
    name: "智慧门锁",
    price: "399",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/05/b1a536cc-553f-4887-84ab-fcebf704c308.png",
    id: "15",
    selectId: "615",
    selectType:
      "开锁方式：指纹+密码+门卡+钥匙+小程序； 指纹容量：50个； 设备材质：6061航空铝； 工作电源：4节5号电池； 颜色：深灰色； 尺寸：40-120mm； 售后服务：质保1年；",
    productList: [
      {
        id: 615,
        productId: 15,
        name: "中移慧居Z1",
        tariff: "399",
        unit: "元",
        specification:
          "开锁方式：指纹+密码+门卡+钥匙+小程序； 指纹容量：50个； 设备材质：6061航空铝； 工作电源：4节5号电池； 颜色：深灰色； 尺寸：40-120mm； 售后服务：质保1年；",
      },
      {
        id: 616,
        productId: 15,
        name: "中移慧居/芯灵CS-P8/F1",
        tariff: "599",
        unit: "元",
        specification:
          "开锁方式：指纹+密码+门卡+钥匙； 安全等级： C级锁芯； 售后服务：2年（2年内可免费换新）； 其他：虚位密码防偷窥、开锁提醒、撬锁语音报警、门锁低电量提醒；",
      },
      {
        id: 617,
        productId: 15,
        name: "中移慧居V6Q",
        tariff: "699",
        unit: "元",
        specification:
          "开锁方式：指纹+密码+门卡+钥匙+远程开锁； 安全等级：C级锁芯； 工作电源：5000毫安锂电池； 售后服务：2年（只换不修）； 其他：全自动门锁、开门无需下压把手、升级降噪锁体、开门更快更安静；",
      },
      {
        id: 618,
        productId: 15,
        name: "中移慧居V8M",
        tariff: "999",
        unit: "元",
        specification:
          "开锁方式：静脉+密码+门卡+钥匙+远程开锁等9种解锁方式； 工作电源：5000毫安锂电池； 售后服务：2年（只换不修）； 其他：全自动智能门锁出入服务、通过WIFI联网、24小时智能监控异常自动抓拍、开门无需下压把手、升级降噪锁体、开门更快更安静；",
      },
      {
        id: 619,
        productId: 15,
        name: "中移科智S21",
        tariff: "999",
        unit: "元",
        specification:
          "开锁方式：人脸+指纹+密码+门卡； 静态电流：≦120uA； 管理员数量：9组； 指纹数量：100枚； 人员：400组； 其他：超清猫眼可视大屏、开锁信息实时推送、开门自动上锁、自带门铃、独立APP+小程序、雷达自动感应无接触开锁、防撬锁报警、多次输错报警、虚伪密码防窥、FPC半导体活体指纹识别。",
      },
      {
        id: 620,
        productId: 15,
        name: "芯灵CS-F9",
        tariff: "998",
        unit: "元",
        specification:
          "开锁方式：3D人脸+指纹+密码+门卡+钥匙+远程开锁； 其他：4.0寸室内大屏、运程可视对讲；",
      },
      {
        id: 621,
        productId: 15,
        name: "芯灵指动脉门锁V18",
        tariff: "1050",
        unit: "元",
        specification:
          "开锁方式：静脉+密码+远程解锁； 其他：双核指静脉猫眼全自动智能门锁、超清可视猫眼、自动抓拍可疑人脸、开锁日志实时推送、不限人群、不挑环境、手指脏、磨损脱皮均可识别，适合全家人使用，指静脉检测比指纹识别更安全；",
      },
      {
        id: 622,
        productId: 15,
        name: "指动脉门锁V18",
        tariff: "499",
        unit: "元",
        specification:
          "开锁方式：指纹+密码+门卡+钥匙+微信小程序+选装无线遥控； 指纹容量：88个； 设备材质：航空铝合金； 工作电源：4节5号电池； 尺寸：摩卡色； 适合门厚：40-120mm； 售后服务：2年（只换不修）；",
      },
    ],
  },
  {
    name: "烟感报警",
    price: "212",
    id: "11",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/05/897a343a-cc6a-4b2d-93e5-a1f1a375d8ba.png",
    selectId: "382",
    selectType:
      "烟雾检测设备NP-FY300-4G含装维1年维护；2年质保，含3年卡，移动千里眼平台使用，含2年保险；1台包装维",
    productList: [
      {
        id: 382,
        productId: 11,
        name: "海康套餐1",
        tariff: "212",
        unit: "元",
        specification:
          "烟雾检测设备NP-FY300-4G含装维1年维护；2年质保，含3年卡，移动千里眼平台使用，含2年保险；1台包装维",
      },
      {
        id: 383,
        productId: 11,
        name: "海康套餐2",
        tariff: "1910",
        unit: "元",
        specification:
          "烟雾检测设备NP-FY300-4G含装维1年维护；2年质保，含3年卡，移动千里眼平台使用，含2年保险；10台包装维",
      },
      {
        id: 384,
        productId: 11,
        name: "海康套餐3",
        tariff: "16873",
        unit: "元",
        specification:
          "烟雾检测设备NP-FY300-4G含装维1年维护；2年质保，含3年卡，移动千里眼平台使用，含2年保险；100台包装维",
      },
      {
        id: 385,
        productId: 11,
        name: "华消套餐1",
        tariff: "207",
        unit: "元",
        specification:
          "烟雾检测设备X1101含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；1台包装维",
      },
      {
        id: 386,
        productId: 11,
        name: "华消套餐2",
        tariff: "1995",
        unit: "元",
        specification:
          "烟雾检测设备X1101含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；10台包装维",
      },
      {
        id: 387,
        productId: 11,
        name: "华消套餐3",
        tariff: "18238",
        unit: "元",
        specification:
          "烟雾检测设备X1101含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；100台包装维",
      },
      {
        id: 388,
        productId: 11,
        name: "汉威套餐1",
        tariff: "214",
        unit: "元",
        specification:
          "烟雾检测设备X1102含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；1台包装维",
      },
      {
        id: 389,
        productId: 11,
        name: "汉威套餐2",
        tariff: "1988",
        unit: "元",
        specification:
          "烟雾检测设备X1102含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；10台包装维",
      },
      {
        id: 390,
        productId: 11,
        name: "汉威套餐3",
        tariff: "17468",
        unit: "元",
        specification:
          "烟雾检测设备X1102含装维；2年质保，含3年卡，移动千里眼平台使用，含2年保险；100台包装维",
      },
    ],
  },
]);
const phoneList = ref([
  {
    name: "组网专线",
    price: "900",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/10/fc4de46e-3f21-40fa-8de4-d8486b3cf91e.jpeg",
    id: "79",
    selectId: "632",
    selectType: "根据客户需求现场评估工作量，按工作量报价收费交付。",
    productList: [
      {
        id: 632,
        productId: 79,
        name: "组网专线套餐",
        tariff: "8888",
        unit: "元",
        specification: "根据客户需求现场评估工作量，按工作量报价收费交付。",
      },
    ],
  },
  {
    name: "云WIFI",
    price: "900",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/4b6644ba-db27-436c-a548-3883ea457d06.png",
    id: "54",
    selectId: "599",
    selectType:
      "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
    productList: [
      {
        id: 599,
        productId: 54,
        name: "e企组网-云无线（室内普通套餐）",
        tariff: "79",
        unit: "元/月/台",
        specification:
          "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
      },
      {
        id: 600,
        productId: 54,
        name: "e企组网-云无线（室内普通套餐）",
        tariff: "59",
        unit: "元/月/台",
        specification:
          "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 601,
        productId: 54,
        name: "e企组网-云无线（室内高密套餐）",
        tariff: "239",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务++合约期24个月",
      },
      {
        id: 602,
        productId: 54,
        name: "e企组网-云无线（室内高密套餐）",
        tariff: "159",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 603,
        productId: 54,
        name: "e企组网-云无线（室外场景套餐）",
        tariff: "24",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
      },
      {
        id: 604,
        productId: 54,
        name: "e企组网-云无线（室外场景套餐）",
        tariff: "36",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 605,
        productId: 54,
        name: "POE扩展包（4口）-1",
        tariff: "49",
        unit: "元/月/台",
        specification:
          "4口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 606,
        productId: 54,
        name: "POE扩展包（4口）-2",
        tariff: "39",
        unit: "元/月/台",
        specification:
          "4口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 607,
        productId: 54,
        name: "POE扩展包（8口）-1",
        tariff: "79",
        unit: "元/月/台",
        specification:
          "8口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 608,
        productId: 54,
        name: "POE扩展包（8口）-2",
        tariff: "49",
        unit: "元/月/台",
        specification:
          "8口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 609,
        productId: 54,
        name: "POE扩展包（16口）-1",
        tariff: "129",
        unit: "元/月/台",
        specification:
          "16口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 610,
        productId: 54,
        name: "POE扩展包（16口）-2",
        tariff: "89",
        unit: "元/月/台",
        specification:
          "16口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 611,
        productId: 54,
        name: "Portal定制-1",
        tariff: "5",
        unit: "元/月/台",
        specification: "每台点位云无线平台Portal定制服务+合约期24个月",
      },
      {
        id: 612,
        productId: 54,
        name: "Portal定制-2",
        tariff: "3.5",
        unit: "元/月/台",
        specification: "每台点位云无线平台Portal定制服务+合约期24个月",
      },
    ],
  },
  {
    name: "专线卫士",
    price: "900",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/a925509c-b616-44a5-9173-7eb0b3edb514.png",
    id: "59",
    selectId: "465",
    selectType: "10万并发连接，每秒新建1万连接，10*GE电口",
    productList: [
      {
        id: 465,
        productId: 59,
        name: "防火墙增强版1",
        tariff: "900",
        unit: "元/月",
        specification: "10万并发连接，每秒新建1万连接，10*GE电口",
      },
      {
        id: 466,
        productId: 59,
        name: "防火墙增强版2",
        tariff: "1200",
        unit: "元/月",
        specification: "20万并发连接，每秒新建1.5万连接，10*GE电口",
      },
      {
        id: 467,
        productId: 59,
        name: "防火墙增强版3",
        tariff: "1800",
        unit: "元/月",
        specification:
          "30万并发连接，每秒新建2万连接，2*10GE（SFP+）+10*GE电口",
      },
      {
        id: 468,
        productId: 59,
        name: "防火墙增强版4",
        tariff: "3000",
        unit: "元/月",
        specification:
          "360万并发连接，每秒新建8万连接，2*10GE（SFP+）+16*GE电口+8*GE SFP接口",
      },
      {
        id: 469,
        productId: 59,
        name: "防火墙优享版1",
        tariff: "900",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 470,
        productId: 59,
        name: "防火墙优享版2",
        tariff: "1000",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 471,
        productId: 59,
        name: "防火墙优享版3",
        tariff: "1200",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 472,
        productId: 59,
        name: "防火墙优享版4",
        tariff: "1800",
        unit: "元/月",
        specification: "130万并发连接，每秒新建7.9万连接，8*GE电口+4*GE光口",
      },
      {
        id: 473,
        productId: 59,
        name: "防火墙优享版5",
        tariff: "2100",
        unit: "元/月",
        specification:
          "250万并发连接，每秒新建10万连接，6*GE电口+2*GE光口+2*10GE(SFP+)接口",
      },
      {
        id: 474,
        productId: 59,
        name: "防火墙基础版1",
        tariff: "900",
        unit: "元/月",
        specification: "50万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 475,
        productId: 59,
        name: "防火墙基础版2",
        tariff: "1200",
        unit: "元/月",
        specification: "50万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 476,
        productId: 59,
        name: "防火墙基础版3",
        tariff: "1500",
        unit: "元/月",
        specification: "100万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 477,
        productId: 59,
        name: "审计版1",
        tariff: "1000",
        unit: "元/月",
        specification:
          "内置256GB固态硬盘，支持180天审计日志存储，6万并发连接，每秒新建15万连接，5GE电口+2GE光口",
      },
      {
        id: 478,
        productId: 59,
        name: "审计版2",
        tariff: "1500",
        unit: "元/月",
        specification:
          "内置256GB固态硬盘，支持180天审计日志存储，6万并发连接，每秒新建30万连接，5GE电口+2combo",
      },
      {
        id: 479,
        productId: 59,
        name: "审计版3",
        tariff: "1800",
        unit: "元/月",
        specification:
          "内置512GB固态硬盘，支持180天审计日志存储6万并发连接，每秒新建50万连接，9GE电口+2GE光口",
      },
    ],
  },
]);
const payList = ref([
  {
    name: "台式扫码盒",
    price: "40",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/29/ae995de9-2987-4d1e-92a2-220c57d1eccd.jpg",
    id: "74",
    selectId: "420",
    selectType: "e商铺",
    productList: [
      {
        id: 420,
        productId: 74,
        name: "台式扫码盒",
        tariff: "40",
        unit: "元/月",
        specification: "e商铺",
      },
    ],
  },
]);
const yunList = ref([
  {
    name: "云电脑（云笔电）",
    price: "59.9",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/05/6de479e2-6922-4ad3-af33-781dbac9a4f2.jpg",
    id: "14",
    selectId: "538",
    selectType: "4核8G 80G系统盘 50M带宽",
    productList: [
      {
        id: 538,
        productId: 14,
        name: "入门版-商务型",
        tariff: "59.9",
        unit: "元/月",
        specification: "4核8G 80G系统盘 50M带宽",
      },
      {
        id: 539,
        productId: 14,
        name: "入门版-经济型",
        tariff: "29.9",
        unit: "元/月",
        specification: "2核4G 80G系统盘 50M带宽",
      },
      {
        id: 540,
        productId: 14,
        name: "公众版-基础版",
        tariff: "50",
        unit: "元/月",
        specification: "2核4G 80G系统盘 50M带宽",
      },
      {
        id: 541,
        productId: 14,
        name: "公众版-标准版",
        tariff: "75",
        unit: "元/月",
        specification: "4核8G 80G系统盘 50M带宽",
      },
      {
        id: 542,
        productId: 14,
        name: "公众版-精英版",
        tariff: "125",
        unit: "元/月",
        specification: "8核16G 80G系统盘 50M带宽",
      },
      {
        id: 543,
        productId: 14,
        name: "自由组合",
        tariff: "8888",
        unit: "元",
        specification: "以实际组合结果为准",
      },
    ],
  },
  {
    name: "云主机",
    price: "8888",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/08/1afc2d15-b6e5-4800-a080-e85b3e44ef78.png",
    id: "17",
    selectId: "521",
    selectType: "以实际规格为准",
    productList: [
      {
        id: 521,
        productId: 17,
        name: "主机配置",
        tariff: "8888",
        unit: "元",
        specification: "以实际规格为准",
      },
    ],
  },
  {
    name: "云桌面",
    price: "100",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/08/0cade01f-bce8-4785-a412-1c542d42f6d6.jpeg",
    id: "18",
    selectId: "333",
    selectType: "2C4G标配版， 100G存储（1-2年）",
    productList: [
      {
        id: 333,
        productId: 18,
        name: "云桌面套餐1",
        tariff: "100",
        unit: "元/月",
        specification: "2C4G标配版， 100G存储（1-2年）",
      },
      {
        id: 334,
        productId: 18,
        name: "云桌面套餐2",
        tariff: "80",
        unit: "元/月",
        specification: "2C4G标配版， 100G存储（3-5年）",
      },
      {
        id: 335,
        productId: 18,
        name: "云桌面套餐3",
        tariff: "150",
        unit: "元/月",
        specification: "4C8G标配版， 100G存储（1-2年）",
      },
      {
        id: 336,
        productId: 18,
        name: "云桌面套餐4",
        tariff: "120",
        unit: "元/月",
        specification: "4C8G标配版， 100G存储（3-5年）",
      },
    ],
  },
  {
    name: "云空间",
    price: "24",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/80d24ffb-a6bc-49ef-90c2-a750fb34b450.png",
    id: "56",
    selectId: "192",
    selectType: "企业人数：50人",
    productList: [
      {
        id: 192,
        productId: 56,
        name: "云空间存储空间：100G",
        tariff: "24",
        unit: "元/月",
        specification: "企业人数：50人",
      },
      {
        id: 193,
        productId: 56,
        name: "云空间存储空间：200G",
        tariff: "48",
        unit: "元/月",
        specification: "企业人数：100人",
      },
    ],
  },
]);
const tellList = ref([
  // {
  //   name: "云识数智名片",
  //   price: "1000",
  //   unit: "元/年",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/8d003883-c6bc-4205-851d-1f1176cf2029.png",
  //   id: "39",
  //   selectId: "546",
  //   selectType: "1-10人（人均100）",
  //   productList: [
  //     {
  //       id: 546,
  //       productId: 39,
  //       name: "数智名片（入门版）",
  //       tariff: "1000",
  //       unit: "元/年",
  //       specification: "1-10人（人均100）",
  //     },
  //     {
  //       id: 547,
  //       productId: 39,
  //       name: "数智名片（进阶版）",
  //       tariff: "2000",
  //       unit: "元/年",
  //       specification: " 1-10人（人均200）",
  //     },
  //     {
  //       id: 548,
  //       productId: 39,
  //       name: "数智名片（旗舰版）",
  //       tariff: "24000",
  //       unit: "元/年",
  //       specification: "24000起步，根据客户需求具体测算报价",
  //     },
  //   ],
  // },
  {
    name: "全媒体",
    price: "0.023",
    unit: "元/次",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/552406a4-476c-4254-85b5-e77828a8c88a.png",
    id: "48",
    selectId: "544",
    selectType: "朋友圈广告",
    productList: [
      {
        id: 544,
        productId: 48,
        name: "朋友圈套餐",
        tariff: "0.023",
        unit: "元/次",
        specification: "朋友圈广告",
      },
      {
        id: 545,
        productId: 48,
        name: "抖音套餐",
        tariff: "0.027",
        unit: "元/次",
        specification: "抖音信息流常规行业",
      },
    ],
  },
  {
    name: "获客助手",
    price: "3",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/1a2510b5-faef-42a8-8704-badc67d2368a.png",
    id: "64",
    selectId: "623",
    selectType: "每月月租形式",
    productList: [
      {
        id: 623,
        productId: 64,
        name: "套餐1",
        tariff: "3",
        unit: "元/月",
        specification: "每月月租形式",
      },
    ],
  },
  // {
  //   name: "发券助手",
  //   price: "8888",
  //   unit: "元",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/13/403453a3-4ea2-4c8d-9195-8ffc412e1182.jpeg",
  //   id: "63",
  //   selectId: "464",
  //   selectType: "每月月租形式",
  //   productList: [
  //     {
  //       id: 464,
  //       productId: 63,
  //       name: "套餐1",
  //       tariff: "8888",
  //       unit: "元",
  //       specification: "每月月租形式",
  //     },
  //   ],
  // },
  // {
  //   name: "集团福袋",
  //   price: "100",
  //   unit: "元",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/13/c3f7a212-8467-4435-968d-1307a8890027.jpeg",
  //   id: "61",
  //   selectId: "225",
  //   selectType: "纯享100元卡券",
  //   productList: [
  //     {
  //       id: 225,
  //       productId: 61,
  //       name: "套餐1",
  //       tariff: "100",
  //       unit: "元",
  //       specification: "纯享100元卡券",
  //     },
  //     {
  //       id: 226,
  //       productId: 61,
  //       name: "套餐2",
  //       tariff: "200",
  //       unit: "元",
  //       specification: "轻享200元卡券",
  //     },
  //     {
  //       id: 227,
  //       productId: 61,
  //       name: "套餐3",
  //       tariff: "500",
  //       unit: "元",
  //       specification: "悦享500元卡券",
  //     },
  //     {
  //       id: 228,
  //       productId: 61,
  //       name: "套餐4",
  //       tariff: "1000",
  //       unit: "元",
  //       specification: "优享1000元卡券",
  //     },
  //     {
  //       id: 229,
  //       productId: 61,
  //       name: "套餐5",
  //       tariff: "5000",
  //       unit: "元",
  //       specification: "尊享5000元卡券",
  //     },
  //   ],
  // },
  // {
  //   name: "奕起嗨智慧经营SaaS云服务",
  //   price: "900",
  //   unit: "元",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/13/4f7551b3-9747-42df-a125-99d9f4485974.jpeg",
  //   id: "65",
  //   selectId: "566",
  //   selectType: "新增1个门店1年期",
  //   productList: [
  //     {
  //       id: 566,
  //       productId: 65,
  //       name: "门店",
  //       tariff: "900",
  //       unit: "元",
  //       specification: "新增1个门店1年期",
  //     },
  //     {
  //       id: 567,
  //       productId: 65,
  //       name: "短信1",
  //       tariff: "100",
  //       unit: "元",
  //       specification: "1000条短信",
  //     },
  //     {
  //       id: 568,
  //       productId: 65,
  //       name: "短信2",
  //       tariff: "180",
  //       unit: "元",
  //       specification: "2000条短信",
  //     },
  //     {
  //       id: 569,
  //       productId: 65,
  //       name: "短信3",
  //       tariff: "420",
  //       unit: "元",
  //       specification: "5000条短信",
  //     },
  //     {
  //       id: 570,
  //       productId: 65,
  //       name: "短信4",
  //       tariff: "750",
  //       unit: "元",
  //       specification: "10000条短信",
  //     },
  //     {
  //       id: 571,
  //       productId: 65,
  //       name: "营销服务1",
  //       tariff: "100",
  //       unit: "元",
  //       specification: "100Hi点",
  //     },
  //     {
  //       id: 572,
  //       productId: 65,
  //       name: "营销服务2",
  //       tariff: "450",
  //       unit: "元",
  //       specification: "500Hi点",
  //     },
  //     {
  //       id: 573,
  //       productId: 65,
  //       name: "营销服务3",
  //       tariff: "800",
  //       unit: "元",
  //       specification: "1000Hi点",
  //     },
  //     {
  //       id: 574,
  //       productId: 65,
  //       name: "营销服务4",
  //       tariff: "1500",
  //       unit: "元",
  //       specification: "2000Hi点",
  //     },
  //     {
  //       id: 575,
  //       productId: 65,
  //       name: "存储空间1",
  //       tariff: "30",
  //       unit: "元",
  //       specification: "1000MB",
  //     },
  //     {
  //       id: 576,
  //       productId: 65,
  //       name: "存储空间2",
  //       tariff: "120",
  //       unit: "元",
  //       specification: "5000MB",
  //     },
  //     {
  //       id: 577,
  //       productId: 65,
  //       name: "播放时长1",
  //       tariff: "12",
  //       unit: "元",
  //       specification: "1000分钟",
  //     },
  //     {
  //       id: 578,
  //       productId: 65,
  //       name: "播放时长2",
  //       tariff: "80",
  //       unit: "元",
  //       specification: "6000分钟",
  //     },
  //     {
  //       id: 579,
  //       productId: 65,
  //       name: "播放时长3",
  //       tariff: "650",
  //       unit: "元",
  //       specification: "60000分钟",
  //     },
  //     {
  //       id: 580,
  //       productId: 65,
  //       name: "图片设计1",
  //       tariff: "280",
  //       unit: "元",
  //       specification: "手机宣传单页设计1张",
  //     },
  //     {
  //       id: 581,
  //       productId: 65,
  //       name: "图片设计2",
  //       tariff: "480",
  //       unit: "元",
  //       specification: "易拉宝海报设计1张",
  //     },
  //     {
  //       id: 582,
  //       productId: 65,
  //       name: "图片设计3",
  //       tariff: "50",
  //       unit: "元",
  //       specification: "海报尺寸扩展1张",
  //     },
  //   ],
  // },
  // {
  //   name: "商显屏",
  //   price: "10",
  //   unit: "元/账号/月",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/445ec854-eb79-4f41-8d30-0e44258d38b1.png",
  //   id: "49",
  //   selectId: "328",
  //   selectType: "提供软件服务，含：信息发布系统+营销模板",
  //   productList: [
  //     {
  //       id: 328,
  //       productId: 49,
  //       name: "高级版",
  //       tariff: "10",
  //       unit: "元/账号/月",
  //       specification: "提供软件服务，含：信息发布系统+营销模板",
  //     },
  //     {
  //       id: 329,
  //       productId: 49,
  //       name: "旗舰版",
  //       tariff: "30",
  //       unit: "元/账号/月",
  //       specification: "件适配服务：提供机顶盒/电视棒等硬件适配服务。",
  //     },
  //     {
  //       id: 330,
  //       productId: 49,
  //       name: "旗舰版PLUS（合同期1年）",
  //       tariff: "450",
  //       unit: "元/账号/月",
  //       specification:
  //         "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
  //     },
  //     {
  //       id: 331,
  //       productId: 49,
  //       name: "旗舰版PLUS（合同期2年）",
  //       tariff: "250",
  //       unit: "元/账号/月",
  //       specification:
  //         "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
  //     },
  //   ],
  // },
  // {
  //   name: "互联网电视",
  //   price: "20",
  //   unit: "元/月",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/10/7315c27a-23ed-4a29-977b-24b0efbecf02.jpeg",
  //   id: "80",
  //   selectId: "635",
  //   selectType: "互联网电视标准规格",
  //   productList: [
  //     {
  //       id: 635,
  //       productId: 80,
  //       name: "互联网电视套餐",
  //       tariff: "20",
  //       unit: "元/月",
  //       specification: "互联网电视标准规格",
  //     },
  //   ],
  // },
]);
const guanList = ref([
  {
    name: "考勤机",
    price: "2700",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/4f47d0c2-499e-4117-acb2-e4db27fa2e84.png",
    id: "55",
    selectId: "291",
    selectType: "1台",
    productList: [
      {
        id: 291,
        productId: 55,
        name: "8寸人脸识别一体机",
        tariff: "2700",
        unit: "元",
        specification: "1台",
      },
      {
        id: 292,
        productId: 55,
        name: "10寸人脸识别一体机",
        tariff: "3100",
        unit: "元",
        specification: "1台",
      },
      {
        id: 293,
        productId: 55,
        name: "安装维护费",
        tariff: "500",
        unit: "元",
        specification:
          "三年维护，仅普通安装，若需要现场土建改造、布线等施工，需另谈",
      },
    ],
  },
  {
    name: "和对讲",
    price: "15",
    unit: "元/1G",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/3ab75251-9ff6-42c0-815e-fb67ca904bd4.png",
    id: "46",
    selectId: "340",
    selectType:
      "Cat1系列（CM12、CM13、CM14、CM15、CM16、CM17、CM18、CM19、CM2）",
    productList: [
      {
        id: 340,
        productId: 46,
        name: "Cat1系列（基础版）",
        tariff: "15",
        unit: "元/1G",
        specification:
          "Cat1系列（CM12、CM13、CM14、CM15、CM16、CM17、CM18、CM19、CM2）",
      },
      {
        id: 341,
        productId: 46,
        name: "C3X系列（语音对讲+定位）",
        tariff: "20",
        unit: "元/1G",
        specification:
          "C3X系列（CM11、C31、C31e、C32、C33、C34、C35、C36、C37、C41、C42）",
      },
      {
        id: 342,
        productId: 46,
        name: "D2X系列（视频对讲）",
        tariff: "50",
        unit: "元/6G",
        specification:
          "D2X系列（D21、D21e、D22、D23、D24、D25、D27、D28、D31、D32）",
      },
      {
        id: 343,
        productId: 46,
        name: "SX系列（执法记录仪）",
        tariff: "90",
        unit: "元/50G",
        specification:
          "S11、S12、S12(64G版)、S13、S14、S15、S17、S18、S19、S1A、S21、S22、S23、S24、S25、S31、S32、S33。",
      },
      {
        id: 344,
        productId: 46,
        name: "H系列（公专融合对讲机 / 工作机）",
        tariff: "120",
        unit: "元/100G",
        specification: "H11（双电）、H12、H13、H24、H25、H28。",
      },
    ],
  },
  {
    name: "新V网",
    price: "2",
    unit: "元/月/成员",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/d2926457-f4c8-499c-ab5f-84ab5e779b81.png",
    id: "53",
    selectId: "300",
    selectType: "新V网融合套餐",
    productList: [
      {
        id: 300,
        productId: 53,
        name: "新V网融合套餐",
        tariff: "2",
        unit: "元/月/成员",
        specification: "新V网融合套餐",
      },
      {
        id: 301,
        productId: 53,
        name: "移动办公-增强版",
        tariff: "10",
        unit: "元/月/用户",
        specification: "移动办公-增强版",
      },
      {
        id: 302,
        productId: 53,
        name: "快通知",
        tariff: "0.1",
        unit: "元/条/月/企业",
        specification: "快通知增值功能包自定义资费套餐",
      },
      {
        id: 303,
        productId: 53,
        name: "公费会议",
        tariff: "0.1",
        unit: "元/分钟/月/企业",
        specification: "公费会议自定义资费套餐",
      },
      {
        id: 304,
        productId: 53,
        name: "公费短信",
        tariff: "0.1",
        unit: "元/条/月/企业",
        specification: "公费短信自定义资费套餐",
      },
    ],
  },
]);
const qiyeList = ref([
  {
    name: "新V网",
    price: "2",
    unit: "元/月/成员",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/d2926457-f4c8-499c-ab5f-84ab5e779b81.png",
    id: "53",
    selectId: "300",
    selectType: "新V网融合套餐",
    productList: [
      {
        id: 300,
        productId: 53,
        name: "新V网融合套餐",
        tariff: "2",
        unit: "元/月/成员",
        specification: "新V网融合套餐",
      },
      {
        id: 301,
        productId: 53,
        name: "移动办公-增强版",
        tariff: "10",
        unit: "元/月/用户",
        specification: "移动办公-增强版",
      },
      {
        id: 302,
        productId: 53,
        name: "快通知",
        tariff: "0.1",
        unit: "元/条/月/企业",
        specification: "快通知增值功能包自定义资费套餐",
      },
      {
        id: 303,
        productId: 53,
        name: "公费会议",
        tariff: "0.1",
        unit: "元/分钟/月/企业",
        specification: "公费会议自定义资费套餐",
      },
      {
        id: 304,
        productId: 53,
        name: "公费短信",
        tariff: "0.1",
        unit: "元/条/月/企业",
        specification: "公费短信自定义资费套餐",
      },
    ],
  },
  {
    name: "5G随e签",
    price: "5.96",
    unit: "元/次",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/05/d3d537d4-cc12-4974-8741-3399493e189c.jpeg",
    id: "13",
    selectId: "396",
    selectType: "50次调用量",
    productList: [
      {
        id: 396,
        productId: 13,
        name: "SAAS（A套餐）",
        tariff: "5.96",
        unit: "元/次",
        specification: "50次调用量",
      },
      {
        id: 397,
        productId: 13,
        name: "SAAS（B套餐）",
        tariff: "4.2",
        unit: "元/次",
        specification: "500次调用量",
      },
      {
        id: 398,
        productId: 13,
        name: "SAAS（C套餐）",
        tariff: "4",
        unit: "元/次",
        specification: "1000次调用量",
      },
      {
        id: 399,
        productId: 13,
        name: "SAAS（D套餐）",
        tariff: "3",
        unit: "元/次",
        specification: "2000次调用量",
      },
      {
        id: 400,
        productId: 13,
        name: "SAAS（E套餐）",
        tariff: "2.6",
        unit: "元/次",
        specification: "5000次调用量",
      },
      {
        id: 401,
        productId: 13,
        name: "API（入门包）",
        tariff: "5",
        unit: "元/次",
        specification: "2000次调用量",
      },
      {
        id: 402,
        productId: 13,
        name: "API（基础包）",
        tariff: "4.5",
        unit: "元/次",
        specification: "5000次调用量",
      },
      {
        id: 403,
        productId: 13,
        name: "API（普通包）",
        tariff: "4",
        unit: "元/次",
        specification: "15000次调用量",
      },
      {
        id: 404,
        productId: 13,
        name: "API（高级包）",
        tariff: "3.5",
        unit: "元/次",
        specification: "50000次调用量",
      },
      {
        id: 405,
        productId: 13,
        name: "API（旗舰包）",
        tariff: "3",
        unit: "元/次",
        specification: "10000次调用量",
      },
    ],
  },
  {
    name: "云空间",
    price: "24",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/80d24ffb-a6bc-49ef-90c2-a750fb34b450.png",
    id: "56",
    selectId: "192",
    selectType: "企业人数：50人",
    productList: [
      {
        id: 192,
        productId: 56,
        name: "云空间存储空间：100G",
        tariff: "24",
        unit: "元/月",
        specification: "企业人数：50人",
      },
      {
        id: 193,
        productId: 56,
        name: "云空间存储空间：200G",
        tariff: "48",
        unit: "元/月",
        specification: "企业人数：100人",
      },
    ],
  },
  {
    name: "云视讯",
    price: "60",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/2f1ebadb-6b1b-4dfd-8180-4b3d7ef74741.png",
    id: "58",
    selectId: "480",
    selectType: "型号-C02",
    productList: [
      {
        id: 480,
        productId: 58,
        name: "高清终端1",
        tariff: "60",
        unit: "元/月",
        specification: "型号-C02",
      },
      {
        id: 481,
        productId: 58,
        name: "高清终端2",
        tariff: "150",
        unit: "元/月",
        specification: "型号-C11Plus",
      },
      {
        id: 482,
        productId: 58,
        name: "高清终端3",
        tariff: "300",
        unit: "元/月",
        specification: "型号-C16",
      },
      {
        id: 483,
        productId: 58,
        name: "桌面终端1",
        tariff: "65",
        unit: "元/月",
        specification: "型号-D31P1us",
      },
      {
        id: 484,
        productId: 58,
        name: "桌面终端2",
        tariff: "80",
        unit: "元/月",
        specification: "型号-D33Plus",
      },
      {
        id: 485,
        productId: 58,
        name: "桌面终端3",
        tariff: "120",
        unit: "元/月",
        specification: "型号-D51",
      },
      {
        id: 486,
        productId: 58,
        name: "智慧屏1",
        tariff: "500",
        unit: "元/月",
        specification: "型号-I65CM31",
      },
      {
        id: 487,
        productId: 58,
        name: "智慧屏2",
        tariff: "550",
        unit: "元/月",
        specification: "型号-I65CM31鹅颈麦版",
      },
      {
        id: 488,
        productId: 58,
        name: "智慧屏3",
        tariff: "600",
        unit: "元/月",
        specification: "型号-I65CM31OPS版",
      },
    ],
  },
  {
    name: "商显屏",
    price: "10",
    unit: "元/账号/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/445ec854-eb79-4f41-8d30-0e44258d38b1.png",
    id: "49",
    selectId: "328",
    selectType: "提供软件服务，含：信息发布系统+营销模板",
    productList: [
      {
        id: 328,
        productId: 49,
        name: "高级版",
        tariff: "10",
        unit: "元/账号/月",
        specification: "提供软件服务，含：信息发布系统+营销模板",
      },
      {
        id: 329,
        productId: 49,
        name: "旗舰版",
        tariff: "30",
        unit: "元/账号/月",
        specification: "件适配服务：提供机顶盒/电视棒等硬件适配服务。",
      },
      {
        id: 330,
        productId: 49,
        name: "旗舰版PLUS（合同期1年）",
        tariff: "450",
        unit: "元/账号/月",
        specification:
          "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
      },
      {
        id: 331,
        productId: 49,
        name: "旗舰版PLUS（合同期2年）",
        tariff: "250",
        unit: "元/账号/月",
        specification:
          "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
      },
    ],
  },
]);
const kuandaiList = ref([
  {
    name: "企业宽带",
    price: "40",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/59a5d056-c382-49d9-96d8-76cbbcf09365.png",
    id: "47",
    selectId: "489",
    selectType: "100M",
    productList: [
      {
        id: 489,
        productId: 47,
        name: "100M宽带",
        tariff: "40",
        unit: "元/月",
        specification: "100M",
      },
      {
        id: 490,
        productId: 47,
        name: "500M宽带",
        tariff: "100",
        unit: "元/月",
        specification: "500M",
      },
      {
        id: 491,
        productId: 47,
        name: "1000M宽带",
        tariff: "150",
        unit: "元/月",
        specification: "1000M",
      },
    ],
  },
  {
    name: "互联网专线",
    price: "5800",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/11/d49ea5e8-8087-404c-8f87-3ef912072638.png",
    id: "38",
    selectId: "494",
    selectType: "10M",
    productList: [
      {
        id: 494,
        productId: 38,
        name: "国际穿透1",
        tariff: "5800",
        unit: "元/月",
        specification: "10M",
      },
      {
        id: 495,
        productId: 38,
        name: "国际穿透2",
        tariff: "25100",
        unit: "元/月",
        specification: "50M",
      },
      {
        id: 496,
        productId: 38,
        name: "国际穿透3",
        tariff: "50100",
        unit: "元/月",
        specification: "100M",
      },
      {
        id: 497,
        productId: 38,
        name: "国际穿透4",
        tariff: "245800",
        unit: "元/月",
        specification: "500M",
      },
      {
        id: 498,
        productId: 38,
        name: "国际穿透5",
        tariff: "430200",
        unit: "元/月",
        specification: "1G",
      },
      {
        id: 499,
        productId: 38,
        name: "国际穿透6",
        tariff: "817400",
        unit: "元/月",
        specification: "2G",
      },
      {
        id: 500,
        productId: 38,
        name: "国际穿透7",
        tariff: "1941200",
        unit: "元/月",
        specification: "5G",
      },
      {
        id: 501,
        productId: 38,
        name: "国际穿透8",
        tariff: "3688300",
        unit: "元/月",
        specification: "10G",
      },
      {
        id: 502,
        productId: 38,
        name: "国内穿透1",
        tariff: "3800",
        unit: "元/月",
        specification: "10M",
      },
      {
        id: 503,
        productId: 38,
        name: "国内穿透2",
        tariff: "17100",
        unit: "元/月",
        specification: "50M",
      },
      {
        id: 504,
        productId: 38,
        name: "国内穿透3",
        tariff: "33700",
        unit: "元/月",
        specification: "100M",
      },
      {
        id: 505,
        productId: 38,
        name: "国内穿透4",
        tariff: "165500",
        unit: "元/月",
        specification: "500M",
      },
      {
        id: 506,
        productId: 38,
        name: "国内穿透5",
        tariff: "284800",
        unit: "元/月",
        specification: "1G",
      },
      {
        id: 507,
        productId: 38,
        name: "国内穿透6",
        tariff: "541200",
        unit: "元/月",
        specification: "2G",
      },
      {
        id: 508,
        productId: 38,
        name: "国内穿透7",
        tariff: "1285300",
        unit: "元/月",
        specification: "5G",
      },
      {
        id: 509,
        productId: 38,
        name: "国内穿透8",
        tariff: "2442100",
        unit: "元/月",
        specification: "10G",
      },
      {
        id: 510,
        productId: 38,
        name: "非穿透1",
        tariff: "3200",
        unit: "元/月",
        specification: "10M",
      },
      {
        id: 511,
        productId: 38,
        name: "非穿透2",
        tariff: "14300",
        unit: "元/月",
        specification: "50M",
      },
      {
        id: 512,
        productId: 38,
        name: "非穿透3",
        tariff: "27400",
        unit: "元/月",
        specification: "100M",
      },
      {
        id: 513,
        productId: 38,
        name: "非穿透4",
        tariff: "134600",
        unit: "元/月",
        specification: "500M",
      },
      {
        id: 514,
        productId: 38,
        name: "非穿透5",
        tariff: "235500",
        unit: "元/月",
        specification: "1G",
      },
      {
        id: 515,
        productId: 38,
        name: "非穿透6",
        tariff: "447400",
        unit: "元/月",
        specification: "2G",
      },
      {
        id: 516,
        productId: 38,
        name: "非穿透7",
        tariff: "1062500",
        unit: "元/月",
        specification: "5G",
      },
      {
        id: 517,
        productId: 38,
        name: "非穿透8",
        tariff: "1618000",
        unit: "元/月",
        specification: "10G",
      },
    ],
  },
  {
    name: "数据专线",
    price: "8888",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/11/ce1c2184-db40-4e8c-97d9-adb4d141b643.png",
    id: "37",
    selectId: "518",
    selectType: "实际带宽速率（M）",
    productList: [
      {
        id: 518,
        productId: 37,
        name: "跨省数据专线-传统",
        tariff: "8888",
        unit: "元",
        specification: "实际带宽速率（M）",
      },
      {
        id: 519,
        productId: 37,
        name: "跨省数据专线-专网",
        tariff: "8888",
        unit: "元",
        specification: "实际带宽速率（M）",
      },
      {
        id: 520,
        productId: 37,
        name: "本地数据专线",
        tariff: "8888",
        unit: "元",
        specification: "实际带宽速率（M）",
      },
    ],
  },
  {
    name: "专线卫士",
    price: "900",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/a925509c-b616-44a5-9173-7eb0b3edb514.png",
    id: "59",
    selectId: "465",
    selectType: "10万并发连接，每秒新建1万连接，10*GE电口",
    productList: [
      {
        id: 465,
        productId: 59,
        name: "防火墙增强版1",
        tariff: "900",
        unit: "元/月",
        specification: "10万并发连接，每秒新建1万连接，10*GE电口",
      },
      {
        id: 466,
        productId: 59,
        name: "防火墙增强版2",
        tariff: "1200",
        unit: "元/月",
        specification: "20万并发连接，每秒新建1.5万连接，10*GE电口",
      },
      {
        id: 467,
        productId: 59,
        name: "防火墙增强版3",
        tariff: "1800",
        unit: "元/月",
        specification:
          "30万并发连接，每秒新建2万连接，2*10GE（SFP+）+10*GE电口",
      },
      {
        id: 468,
        productId: 59,
        name: "防火墙增强版4",
        tariff: "3000",
        unit: "元/月",
        specification:
          "360万并发连接，每秒新建8万连接，2*10GE（SFP+）+16*GE电口+8*GE SFP接口",
      },
      {
        id: 469,
        productId: 59,
        name: "防火墙优享版1",
        tariff: "900",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 470,
        productId: 59,
        name: "防火墙优享版2",
        tariff: "1000",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 471,
        productId: 59,
        name: "防火墙优享版3",
        tariff: "1200",
        unit: "元/月",
        specification:
          "40万并发连接，每秒新建1.2万连接，5GE电口+1光电复用Combo",
      },
      {
        id: 472,
        productId: 59,
        name: "防火墙优享版4",
        tariff: "1800",
        unit: "元/月",
        specification: "130万并发连接，每秒新建7.9万连接，8*GE电口+4*GE光口",
      },
      {
        id: 473,
        productId: 59,
        name: "防火墙优享版5",
        tariff: "2100",
        unit: "元/月",
        specification:
          "250万并发连接，每秒新建10万连接，6*GE电口+2*GE光口+2*10GE(SFP+)接口",
      },
      {
        id: 474,
        productId: 59,
        name: "防火墙基础版1",
        tariff: "900",
        unit: "元/月",
        specification: "50万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 475,
        productId: 59,
        name: "防火墙基础版2",
        tariff: "1200",
        unit: "元/月",
        specification: "50万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 476,
        productId: 59,
        name: "防火墙基础版3",
        tariff: "1500",
        unit: "元/月",
        specification: "100万并发连接，每秒新建2万连接，8*GE电口+2*GE SFP接口",
      },
      {
        id: 477,
        productId: 59,
        name: "审计版1",
        tariff: "1000",
        unit: "元/月",
        specification:
          "内置256GB固态硬盘，支持180天审计日志存储，6万并发连接，每秒新建15万连接，5GE电口+2GE光口",
      },
      {
        id: 478,
        productId: 59,
        name: "审计版2",
        tariff: "1500",
        unit: "元/月",
        specification:
          "内置256GB固态硬盘，支持180天审计日志存储，6万并发连接，每秒新建30万连接，5GE电口+2combo",
      },
      {
        id: 479,
        productId: 59,
        name: "审计版3",
        tariff: "1800",
        unit: "元/月",
        specification:
          "内置512GB固态硬盘，支持180天审计日志存储6万并发连接，每秒新建50万连接，9GE电口+2GE光口",
      },
    ],
  },
]);
const moreList = ref([
  // {
  //   name: "企业视频彩铃",
  //   price: "8",
  //   unit: "元/月/成员",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/87e4e5db-5d7c-4ed7-afdd-82ddaa6295ba.png",
  //   id: "52",
  //   selectId: "332",
  //   selectType: "按次收取铃音制作更换服务费",
  //   productList: [
  //     {
  //       id: 332,
  //       productId: 52,
  //       name: "套餐一",
  //       tariff: "8",
  //       unit: "元/月/成员",
  //       specification: "按次收取铃音制作更换服务费",
  //     },
  //   ],
  // },
  // {
  //   name: "点亮屏幕（政企版）",
  //   price: "20",
  //   unit: "元/月/号码",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/41f13e9a-86af-4b8c-8d87-fa7bb15922f5.png",
  //   id: "45",
  //   selectId: "345",
  //   selectType: "按成员数向集团统付收费，每月可制作视频1次。",
  //   productList: [
  //     {
  //       id: 345,
  //       productId: 45,
  //       name: "点亮屏幕政企版-月包",
  //       tariff: "20",
  //       unit: "元/月/号码",
  //       specification: "按成员数向集团统付收费，每月可制作视频1次。",
  //     },
  //   ],
  // },
  // {
  //   name: "AI智接",
  //   price: "18",
  //   unit: "元/月",
  //   img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/1b6a152b-398c-43d7-b3da-1fdbda0e62d6.png",
  //   id: "43",
  //   selectId: "591",
  //   selectType:
  //     "语音交互+100%智能代接+大模型文本润色、通话总结+消息提醒+通话分析",
  //   productList: [
  //     {
  //       id: 591,
  //       productId: 43,
  //       name: "AI智接-语音版",
  //       tariff: "18",
  //       unit: "元/月",
  //       specification:
  //         "语音交互+100%智能代接+大模型文本润色、通话总结+消息提醒+通话分析",
  //     },
  //     {
  //       id: 592,
  //       productId: 43,
  //       name: "AI智接-视频版",
  //       tariff: "38",
  //       unit: "元/月",
  //       specification: "增加数字人视频交互功能，新功能迭代中。",
  //     },
  //   ],
  // },
  {
    name: "商显屏",
    price: "10",
    unit: "元/账号/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/445ec854-eb79-4f41-8d30-0e44258d38b1.png",
    id: "49",
    selectId: "328",
    selectType: "提供软件服务，含：信息发布系统+营销模板",
    productList: [
      {
        id: 328,
        productId: 49,
        name: "高级版",
        tariff: "10",
        unit: "元/账号/月",
        specification: "提供软件服务，含：信息发布系统+营销模板",
      },
      {
        id: 329,
        productId: 49,
        name: "旗舰版",
        tariff: "30",
        unit: "元/账号/月",
        specification: "件适配服务：提供机顶盒/电视棒等硬件适配服务。",
      },
      {
        id: 330,
        productId: 49,
        name: "旗舰版PLUS（合同期1年）",
        tariff: "450",
        unit: "元/账号/月",
        specification:
          "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
      },
      {
        id: 331,
        productId: 49,
        name: "旗舰版PLUS（合同期2年）",
        tariff: "250",
        unit: "元/账号/月",
        specification:
          "提供一体化的软件及硬件适配服务，含一体机屏内幕集成软件服务：信息发布系统+营销模板",
      },
    ],
  },
  {
    name: "互联网电视",
    price: "20",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/10/7315c27a-23ed-4a29-977b-24b0efbecf02.jpeg",
    id: "80",
    selectId: "635",
    selectType: "互联网电视标准规格",
    productList: [
      {
        id: 635,
        productId: 80,
        name: "互联网电视套餐",
        tariff: "20",
        unit: "元/月",
        specification: "互联网电视标准规格",
      },
    ],
  },
  {
    name: "云音响",
    price: "40",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/29/c4cf5196-0778-4fd9-a849-032734ebfbe4.jpeg",
    id: "75",
    selectType: "e商铺",
    selectId: "419",
    productList: [
      {
        id: 419,
        productId: 75,
        name: "云音响",
        tariff: "40",
        unit: "元/月",
        specification: "e商铺",
      },
    ],
  },
  {
    name: "台式扫码盒",
    price: "40",
    unit: "元/月",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/29/ae995de9-2987-4d1e-92a2-220c57d1eccd.jpg",
    id: "74",
    selectId: "420",
    selectType: "e商铺",
    productList: [
      {
        id: 420,
        productId: 74,
        name: "台式扫码盒",
        tariff: "40",
        unit: "元/月",
        specification: "e商铺",
      },
    ],
  },
]);
const zuwangList = ref([
  {
    name: "企业宽带",
    price: "8888",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/10/fc4de46e-3f21-40fa-8de4-d8486b3cf91e.jpeg",
    id: "79",
    selectId: "632",
    selectType: "根据客户需求现场评估工作量，按工作量报价收费交付。",
    productList: [
      {
        id: 632,
        productId: 79,
        name: "组网专线套餐",
        tariff: "8888",
        unit: "元",
        specification: "根据客户需求现场评估工作量，按工作量报价收费交付。",
      },
    ],
  },
  {
    name: "云WIFI",
    price: "79",
    unit: "元/月/台",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/4b6644ba-db27-436c-a548-3883ea457d06.png",
    id: "74",
    selectId: "599",
    selectType:
      "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
    productList: [
      {
        id: 599,
        productId: 54,
        name: "e企组网-云无线（室内普通套餐）",
        tariff: "79",
        unit: "元/月/台",
        specification:
          "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
      },
      {
        id: 600,
        productId: 54,
        name: "e企组网-云无线（室内普通套餐）",
        tariff: "59",
        unit: "元/月/台",
        specification:
          "云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 601,
        productId: 54,
        name: "e企组网-云无线（室内高密套餐）",
        tariff: "239",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务++合约期24个月",
      },
      {
        id: 602,
        productId: 54,
        name: "e企组网-云无线（室内高密套餐）",
        tariff: "159",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 603,
        productId: 54,
        name: "e企组网-云无线（室外场景套餐）",
        tariff: "24",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期24个月",
      },
      {
        id: 604,
        productId: 54,
        name: "e企组网-云无线（室外场景套餐）",
        tariff: "36",
        unit: "元/月/台",
        specification:
          "包括云无线热点*1+装维服务（含10米内布线及设备调测）+云无线平台服务+合约期36个月",
      },
      {
        id: 605,
        productId: 54,
        name: "POE扩展包（4口）-1",
        tariff: "49",
        unit: "元/月/台",
        specification:
          "4口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 606,
        productId: 54,
        name: "POE扩展包（4口）-2",
        tariff: "39",
        unit: "元/月/台",
        specification:
          "4口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 607,
        productId: 54,
        name: "POE扩展包（8口）-1",
        tariff: "79",
        unit: "元/月/台",
        specification:
          "8口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 608,
        productId: 54,
        name: "POE扩展包（8口）-2",
        tariff: "49",
        unit: "元/月/台",
        specification:
          "8口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 609,
        productId: 54,
        name: "POE扩展包（16口）-1",
        tariff: "129",
        unit: "元/月/台",
        specification:
          "16口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期24个月",
      },
      {
        id: 610,
        productId: 54,
        name: "POE扩展包（16口）-2",
        tariff: "89",
        unit: "元/月/台",
        specification:
          "16口PoE交换机*1+装维服务（含10米内布线及设备调测）+合约期36个月",
      },
      {
        id: 611,
        productId: 54,
        name: "Portal定制-1",
        tariff: "5",
        unit: "元/月/台",
        specification: "每台点位云无线平台Portal定制服务+合约期24个月",
      },
      {
        id: 612,
        productId: 54,
        name: "Portal定制-2",
        tariff: "3.5",
        unit: "元/月/台",
        specification: "每台点位云无线平台Portal定制服务+合约期24个月",
      },
    ],
  },
  {
    name: "无线路由器",
    price: "480",
    unit: "元/2年",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/712858c6-d2b4-4385-8ff6-4e24e60ce97c.png",
    id: "51",
    selectId: "325",
    selectType: "200M企业宽带+1900M千兆路由器",
    productList: [
      {
        id: 325,
        productId: 51,
        name: "480元包2年200M一网通+组网",
        tariff: "480",
        unit: "元/2年",
        specification: "200M企业宽带+1900M千兆路由器",
      },
      {
        id: 326,
        productId: 51,
        name: "720元包2年1000M一网通+组网",
        tariff: "720",
        unit: "元/2年",
        specification: "1000M企业宽带+1900M千兆路由器",
      },
      {
        id: 327,
        productId: 51,
        name: "180元包1年组网",
        tariff: "180",
        unit: "元/年",
        specification: "1900M千兆路由器",
      },
    ],
  },
  {
    name: "专线卫士",
    price: "1888",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/12/04/0cf2f36d-8b3c-4c82-9d7a-2f97a34c5bb8.png",
    id: "40",
    selectId: "583",
    selectType:
      "5G流量不限量，月达400G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
    productList: [
      {
        id: 583,
        productId: 40,
        name: "5G快线300G档（享400G高速流量）-1年",
        tariff: "1888",
        unit: "元",
        specification:
          "5G流量不限量，月达400G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
      {
        id: 584,
        productId: 40,
        name: "5G快线300G档（享400G高速流量）-2年",
        tariff: "2488",
        unit: "元",
        specification:
          "5G流量不限量，月达400G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
      {
        id: 585,
        productId: 40,
        name: "5G快线500G档（享600G高速流量）-1年",
        tariff: "2088",
        unit: "元",
        specification:
          "5G流量不限量，月达600G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
      {
        id: 586,
        productId: 40,
        name: "5G快线500G档（享600G高速流量）-2年",
        tariff: "2988",
        unit: "元",
        specification:
          "5G流量不限量，月达600G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
      {
        id: 587,
        productId: 40,
        name: "5G快线800G档（享900G高速流量）-1年",
        tariff: "2588",
        unit: "元",
        specification:
          "5G流量不限量，月达900G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
      {
        id: 588,
        productId: 40,
        name: "5G快线800G档（享900G高速流量）-2年",
        tariff: "3688",
        unit: "元",
        specification:
          "5G流量不限量，月达900G限速至10M，1T限速至1M，提供Onecyber平台，配套5G终端一台",
      },
    ],
  },
  {
    name: "专线卫士",
    price: "35",
    unit: "元",
    img: "https://smart.jsisi.cn:8099/portal/resource/2024/11/12/6ca76d6f-348d-46aa-a36d-c04ee36570a6.png",
    id: "44",
    selectId: "346",
    selectType: "FTTR-H主*1+FTTR-H从*1",
    productList: [
      {
        id: 346,
        productId: 44,
        name: "1000M套餐1",
        tariff: "35",
        unit: "元",
        specification: "FTTR-H主*1+FTTR-H从*1",
      },
      {
        id: 347,
        productId: 44,
        name: "1000M套餐2",
        tariff: "45",
        unit: "元",
        specification: "FTTR-H主*1+FTTR-H从*2",
      },
      {
        id: 348,
        productId: 44,
        name: "1000M套餐3",
        tariff: "55",
        unit: "元",
        specification: "FTTR-H主*1+FTTR-H从*3",
      },
      {
        id: 349,
        productId: 44,
        name: "1000M套餐4",
        tariff: "65",
        unit: "元",
        specification: "FTTR-H主*1+FTTR-H从*4",
      },
      {
        id: 350,
        productId: 44,
        name: "1000M套餐5",
        tariff: "45",
        unit: "元",
        specification: "FTTR-B主*1+FTTR-B从*1",
      },
      {
        id: 351,
        productId: 44,
        name: "1000M套餐6",
        tariff: "55",
        unit: "元",
        specification: "FTTR-B主*1+FTTR-B从*2",
      },
      {
        id: 352,
        productId: 44,
        name: "1000M套餐7",
        tariff: "65",
        unit: "元",
        specification: "FTTR-B主*1+FTTR-H从*3",
      },
      {
        id: 353,
        productId: 44,
        name: "1000M套餐8",
        tariff: "75",
        unit: "元",
        specification: "FTTR-B主*1+FTTR-B从*4",
      },
    ],
  },
]);
function change(v) {
  isActive.value = v;
}
const getModuId = (e) => {
  if (e.target.checked == true) {
    movalue.value.push(e.target.value);
  } else {
    movalue.value = movalue.value.filter((item) => item != e.target.value);
  }
  checkAll.value = movalue.value.length == firstList.value.length;
  isIndeterminate.value =
    movalue.value.length > 0 && movalue.value.length < firstList.value.length;
};
const getModuId1 = (e) => {
  if (e.target.checked == true) {
    movalue1.value.push(e.target.value);
  } else {
    movalue1.value = movalue1.value.filter((item) => item != e.target.value);
  }
  checkAll1.value = movalue1.value.length == phoneList.value.length;
  isIndeterminate1.value =
    movalue1.value.length > 0 && movalue1.value.length < phoneList.value.length;
};
const buy = () => {
  eventBus.emit("checkAll");
  eventBus.emit("deletePro");
  showSelect.value = true;
};
const getModuId2 = (e) => {
  if (e.target.checked == true) {
    movalue2.value.push(e.target.value);
  } else {
    movalue2.value = movalue2.value.filter((item) => item != e.target.value);
  }
  checkAll2.value = movalue2.value.length == moreList.value.length;
  isIndeterminate2.value =
    movalue2.value.length > 0 && movalue2.value.length < moreList.value.length;
};
const getModuId3 = (e) => {
  if (e.target.checked == true) {
    movalue3.value.push(e.target.value);
  } else {
    movalue3.value = movalue3.value.filter((item) => item != e.target.value);
  }
  checkAll3.value = movalue3.value.length == guanList.value.length;
  isIndeterminate3.value =
    movalue3.value.length > 0 && movalue3.value.length < guanList.value.length;
};
const getModuId4 = (e) => {
  if (e.target.checked == true) {
    movalue4.value.push(e.target.value);
  } else {
    movalue4.value = movalue4.value.filter((item) => item != e.target.value);
  }
  checkAll4.value = movalue4.value.length == tellList.value.length;
  isIndeterminate4.value =
    movalue4.value.length > 0 && movalue4.value.length < tellList.value.length;
};
const router = useRouter();
const data = reactive({
  msgObj:'',
  showDownloadModal:false,
  showDownloadForm:false,
})
const bornList = () => {
  let arr = [
    ...moreList.value,
    ...firstList.value,
    ...tellList.value,
    ...guanList.value,
    ...phoneList.value,
  ];
  let arr1 = [
    ...movalue1.value,
    ...movalue2.value,
    ...movalue3.value,
    ...movalue4.value,
    ...movalue.value,
  ];
  const filteredArr = arr.filter((item) => arr1.includes(item.id));
  let list = [];
  filteredArr.forEach((item) => {
    list.push({
      productId: item.id,
      tariffId: item.selectId,
      productQuantity: 1,
    });
  });
  let params = {
    productPackageLists: list,
  };

  addShopPro(params).then((res) => {
    router.push({
      name: "buyListPage",
    });
  });
};
const downloadBtn = (e) => {
  //loadShow.value = true;
  getNewDownCount({
    businessId: route.query.id,
    businessType: 3,
  }).then((res) => {
    if (res.data) {
      const href = e;
      const downName = e.name;
      let windowOrigin = window.location.origin;
			let token = localStorage.getItem("token");
			window.open(windowOrigin + href + "?token=" + token);
    } else {
      if(res.msg.includes('5')){
        data.msgObj = {
          applyTimes:1,
          msg:res.msg,
          fullPath:route.fullPath
        }
      }else{
        data.msgObj = {
          applyTimes:2,
          msg:res.msg,
          fullPath:route.fullPath
        }
      }
      data.showDownloadModal = true;
    }
  });
};
const fileShow = (val) => {
  let fileType = val.name.split('.')[1]
  // console.log('fileType',fileType);
  if(fileType == 'pdf'){
    let windowOrigin = window.location.origin;
    let token = localStorage.getItem("token");
    let newHref = val.url;
    if(val.url.includes(windowOrigin)){
      newHref = "/portal" + val.url.split(windowOrigin)[1]
    }
    const newpage = Router.resolve({
      name: "lookPdf",
      query: {
        urlMsg: encodeURIComponent(
          windowOrigin + newHref + "?token=" + token
        ),
        urlName: val.name,
      },
    });
    window.open(newpage.href, "_blank");
  }else{
    loadShow.value = true;
    loadShowTitle.value = '附件加载中'
    pptTopdf({
      filePath: val.path,
      fileUrl: val.url,
    }).then((res) => {
      loadShow.value = false;
      if (res.code == 200) {
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = res.data;
        if(res.data.includes(windowOrigin)){
      	  newHref = "/portal" + res.data.split(windowOrigin)[1]
        }
        const newpage = Router.resolve({
          name: "lookPdf",
          query: {
            urlMsg: encodeURIComponent(
              windowOrigin + newHref + "?token=" + token
            ),
            urlName: val.name,
          },
        });
        window.open(newpage.href, "_blank");
      }
    });
  }
};

const shareLink = (item) => {
  console.log('item',item);
  loadShow.value = true;
  loadShowTitle.value = '生成链接中'
  shareTitle.value = `【麒麟平台】${item.name}`
  let fileType = item.name.split('.')[1]
  // console.log('fileType',fileType);
  if(fileType == 'pdf'){
    let windowOrigin = window.location.origin;
    let token = localStorage.getItem("token");
    let newHref = item.url;
    if(item.url.includes(windowOrigin)){
      newHref = item.url.split(windowOrigin)[1]
    }else{
      let url = item.url.split('/portal')
      newHref = url[1]
    }
    link.value = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(windowOrigin + newHref + "?token=" + token)}&urlName=${item.name}`
    loadShow.value = false;
    shareVisable.value = true
  }else{
    pptTopdf({
      filePath: item.path,
      fileUrl: item.url,
    }).then((res) => {
      if (res.code == 200) {
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = res.data;
        if(res.data.includes(windowOrigin)){
      	  newHref = res.data.split(windowOrigin)[1]
        }else{
          let url = res.data.split('/portal')
          newHref = url[1]
        }
        link.value = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(windowOrigin + newHref + "?token=" + token)}&urlName=${item.name}`
        loadShow.value = false;
        shareVisable.value = true
      }else{
        loadShow.value = false;
      }
    });
    return false;
  }
}
const copy = async() => {
  const { toClipboard } = clipboard3()
  // console.log('navigator',navigator);
  await toClipboard(link.value)
  // navigator.clipboard.writeText(data.link)
  message.success('复制成功')
}
const shareVisableClose = () => {
  shareVisable.value = false
  link.value = ''
  shareTitle.value = ''
}
// 下载超限提示弹窗取消按钮
const downloadModalCancel = () => {
  data.showDownloadModal = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  data.showDownloadModal = false;
  data.showDownloadForm = true;
};
const downloadFormCancel = () => {
  data.showDownloadForm = false;
};
const downloadFormConfirm = () => {
  data.showDownloadForm = false;
};
const tabList = ref([]);
const rules = {
  name: [
    {
      required: true,
      message: "请输入商机名称",
      trigger: "blur",
    },
  ],
  code: [
    {
      required: true,
      message: "请输入商机编号",
      trigger: "blur",
    },
  ],
  categoryId: [
    {
      required: true,
      message: "请选中所属行业",
      trigger: "blur",
    },
  ],
  estimatedAmount: [
    {
      required: true,
      message: "请输入预计金额",
      trigger: "blur",
    },
  ],
};
const summaryDesc = (rule, value) => {
  return new Promise((resolve, reject) => {
    // 简化字符串处理逻辑
    const trimmedValue = value ? value.trim() : "";
    if (trimmedValue.length > 50) {
      reject(new Error("长度不得超过五十"));
    } else {
      resolve();
    }
  });
};
const summaryDescMoney = (rule, value) => {
  return new Promise((resolve, reject) => {
    // 简化字符串处理逻辑
    const trimmedValue = value ? value.trim() : "";

    // 检查是否包含小数点，并据此判断是否可以以零开头
    const hasDecimalPoint = trimmedValue.includes(".");
    const startsWithZero = trimmedValue.startsWith("0");

    // 如果以零开头且不含小数点，则只允许输入为 "0"
    if (startsWithZero && !hasDecimalPoint && trimmedValue !== "0") {
      reject(new Error("金额不能以0开头"));
    } else {
      const numericValue = parseFloat(trimmedValue);
      if (numericValue <= 0 || numericValue > 100000000) {
        // 检查是否为非数字、小于等于0或大于100000000
        reject(new Error("请输入有效的金额（大于0且不超过100000000）"));
      } else {
        resolve(); // 所有验证通过
      }
    }
  });
};
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
const changeTypeKuan = (e) => {
  kuandaiList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeLi = (e) => {
  guanList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeMore = (e) => {
  moreList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypePay = (e) => {
  payList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypePhone = (e) => {
  phoneList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeQi = (e) => {
  qiyeList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeTell = (e) => {
  tellList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeWang = (e) => {
  zuwangList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeTypeYun = (e) => {
  yunList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
const changeType = (e) => {
  firstList.value.forEach((item) => {
    item.productList.forEach((value) => {
      if (value.id == e) {
        item.price = value.tariff;
        item.unit = value.unit;
        item.selectId = value.id;
      }
    });
  });
};
getTarde();
const previewVisible = ref(false);
const formState = reactive({
  name: "",
  code: "",
  categoryId: undefined,
  estimatedAmount: "",
  solutionId: undefined,
  status: undefined,
});
const closeModal = () => {
  previewVisible.value = false;
  formRef.value.resetFields();
};
const onSubmit = (val) => {
  formRef.value
    .validate()
    .then(() => {
      formState.solutionId = route.query.id.toString();
      formState.status = val;
      getIntroduce(toRaw(formState)).then((res) => {
        if (res.code == 200) {
          formRef.value.resetFields();
          previewVisible.value = false;
          if (val == 0) {
            message.success("保存成功");
          } else {
            message.success("申请成功");
          }
        } else {
          message.error(res.msg);
        }
      });
    })
    .catch((err) => {
      console.log("error", err);
    });
};
const formRef = ref();
const dataDeal = (value) => {
  if (value) return value;
  return "-";
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const currentAnchor = ref("#desc");
const selectValue = ref("");
const scrollStep = ref(100);
const back = () => {
  Router.back(-1);
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref({sceneProductList:[]});

const dealClassify = computed(() => {
  return function () {
    let arr = [
      detailData.value.classifyName,
      detailData.value.typeName,
      detailData.value.labelName,
      detailData.value.tagName,
      detailData.value.tallyName,
    ];
    // console.log('arr', arr);
    let newArr = [];
    arr.forEach((item) => {
      if (item && item != null) {
        newArr.push(item);
      }
    });
    return newArr.join(" / ");
  };
});

const jumpTo = (item) => {
  Router.push({
    query: {
      id: item.productId,
    },
    name: "productDetail",
  });
};

const valueData = ref([]);
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "1",
  }).then((res) => {
    getData();
    eventBus.emit("cartRefresh");
  });
};
const getData = () => {
  if (route.query.id) {
    // if (route.query.id == "29") {
    //   isMarket.value = true;
    // }
    getSceneDetail(route.query.id).then((res) => {
        res.data.createTime = res.data.createTime.slice(0, 10);
        if (res.data.applicationMarkets == null) return;
        res.data.markets = [];
        if (res.data.applicationMarkets.includes("1")) {
          // res.data.markets.push("行业市场");
        }
        if (res.data.applicationMarkets.includes("2")) {
          res.data.markets.push("商客市场");
        }
        if (res.data.applicationMarkets.includes("3")) {
          res.data.markets.push("Hdict市场");
        }
        if (res.data.applicationMarkets.includes("4")) {
          // res.data.markets.push("移动云市场");
        }
        res.data.markets = res.data.markets.join(",");
        let anchorInfo = [
          // {
          //   key: "productList",
          //   href: "#productList",
          //   title: "产品清单",
          //   type: 2,
          // },
          {
            key: "desc",
            href: "#desc",
            title: "产品推荐",
            type: 1,
          },
          {
            key: "sceneCase",
            href: "#sceneCase",
            title: "场景案例",
            type: 4,
          },
          {
            key: "marketWords",
            href: "#marketWords",
            title: "营销话术",
            type: 3,
          },
          {
            key: "download",
            href: "#download",
            title: "产品附件",
            type: 4,
          },
        ];
        if (isMarket.value == true) {
          anchorInfo = [
            {
              key: "desc",
              href: "#desc",
              title: "产品推荐",
              type: 1,
            },
            {
              key: "productList",
              href: "#productList",
              title: "产品清单",
              type: 2,
            },
            {
              key: "marketWords",
              href: "#marketWords",
              title: "营销话术",
              type: 3,
            },
            {
              key: "sceneCase",
              href: "#sceneCase",
              title: "场景案例",
              type: 4,
            },
            {
              key: "download",
              href: "#download",
              title: "产品附件",
              type: 4,
            },
          ];
        }
        if (res.data.typeName != null) {
          res.data.typeName = res.data.typeName.split(",");
        }
        anchorList.value = anchorInfo
        if (
          res.data.caseList.length == 0 &&
          res.data.demandSchemeList?.length == 0
        ) {
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "场景案例" && item.title != "产品推荐";
          });
        } else if (res.data.caseList.length == 0) {
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "场景案例";
          });
        } else if (res.data.demandSchemeList?.length == 0) {
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "产品推荐";
          });
        } else {
          anchorList.value = anchorList.value;
        }

        if (res.data?.demandSchemeList?.length == 0) {
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "产品推荐";
          });
        } 
        detailData.value = res.data;
        if (!res.data?.fileList || res.data?.fileList?.length == 0) {
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "产品附件";
          });
        } 
          
        if(res.data.demandSchemeList){
          let arr = res.data.demandSchemeList.map((item=>{
            return item.name
          }))
          arr = arr.join(',')
          detailData.value.list = arr
          programList.value = res.data.demandSchemeList
        }
        detailData.value.sceneProductList = res.data.productClassifyList?.[0]?.productLists ? res.data.productClassifyList : []        
        detailData.value.sceneProductList.forEach(item=>{
          if(item.products){
            let list = [...item.products].sort((a,b)=>{
              return b.mainlyPromote-a.mainlyPromote
            })
            item.products = list
          }
        })

        if(detailData.value?.sceneProductList?.length == 0 && detailData.value?.demandSchemeList?.length == 0){
          anchorList.value = anchorList.value.filter((item) => {
            return item.title != "产品推荐";
          });
        }
        // let arr = res.data.demandSchemeList.map((item) => item.name);
        // arr = arr.join(",");
        // detailData.value.list = arr;
        // if (res.data.demandSchemeList) {
        //   programList.value = res.data.demandSchemeList;
        // }
      })
      .catch((err) => {
        console.log(err);
      });
  }
};

const getContainer = () => {
  return document.getElementById("#anchorContent");
};

const isShow = ref("desc");
const anchorList = ref([]);

const Router = useRouter();
const tabsActiveKey = ref(1)
const tabsChange = (val) => {
  tabsActiveKey.value = val
}
const { Link } = Anchor;
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "start",
      behavior: "smooth",
    });
  isShow.value = href;
};
const toBuy = () => {
  addShop({ productId: route.query.id, type: "1" })
    .then((res) => {
      getData();
      eventBus.emit("cartRefresh");
    })
    .catch((err) => {
      console.log(err);
    });
};

const toDiy = () => {
  toShopList({
    productShoppingCarts: [{ productId: route.query.id, type: "1" }],
    source: "2",
  })
    .then((res) => {
      // Router.push({
      //   name: "buyListPage",
      // });
      Router.push({
      	query: {
      		type: 10,
      	},
      	name: "newAllProject",
      });
    })
    .catch((error) => {});
};

const dealData = (e, type) => {
  if (e === undefined || e === null || e === "" || e >= 8888) {
    if (type == 1) return "以具体业务规格为准";
    if (type == 2) return "以具体业务定价为准";
  } else {
    return e;
  }
};
eventBus.on("scnarioDetailRefresh", getData);
</script>
<style lang="scss" scoped>
.shareBody{
  display: flex;
  justify-content: space-around;
  align-items: center;
  .shareContent{
    flex: 1;
    display: flex;
    align-items: center;
    >img{
      width: 32px;
      height: 32px;
    }
    >.shareContent-link{
      min-width: 160px;
      max-width: 320px;
      >div{
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      >div:last-child{
        text-indent: 0.6em;
      }
    }
  }
  .copyLink{
    width: 74px;
    padding: 4px 9px;
    border-radius: 8px;
    background-color: #96cfed
  }
}
@import "./index.scss";
</style>

<style lang="scss">
.productClassTabs{
  height: 60px;
}
.linkIcon{
  background-color: #fff;
  width:37px;
  height:37px;
  border-radius: 18px;
  justify-content: center;
  align-items: center;
  margin-bottom:7px;
}
.ant-select {
  width: 200px;
}

.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}

.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: rgba(214, 228, 255, 0.6);
    border-radius: 2px 2px 2px 2px;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    // width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d6886 !important;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.reqProgram {
  display: flex;
  flex-flow: column;
  margin-top: 39px;
  .reqProgram-box {
    display: flex;
    flex-flow: column;
    padding: 24px 32px;
    background-color: #fff;
    margin-bottom: 32px;
    // padding-top: 0;
    .reqProgram-box-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 20px;
      color: #2e3852;
      margin-bottom: 10px;
    }
    .reqProgram-box-description {
      text-indent: 20px;
      margin: 10px 0;
    }
    .taglist {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .tag {
        padding: 0 8px;
        margin-right: 6px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #236cff;
        background-color: #d3dff9;
        border: 1px solid #236cff;
        border-radius: 2px;
      }
    }
    .line {
      height: 0px;
      border: 1px solid #dae2f5;
      margin: 10px 0;
    }
    .reqProgram-box-suggestList {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .reqProgram-box-suggestList-content {
        position: relative;
        width: calc(50% - 20px);
        display: flex;
        align-items: center;
        padding: 16px 24px;
        margin-bottom: 16px;
        font-weight: 500;
        font-size: 18px;
        color: #2e3852;
        background: linear-gradient(
          163deg,
          #f1f3f6 0%,
          #f6f7f9 38%,
          #ffffff 100%
        );
        box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
          -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
          .mainlyPromote{
            position: absolute;
            right: -3px;
            top: -2px;
            // transform: rotate(45deg);
            // font-size: 12px;
          }
        > .name {
          height: 49px;
          line-height: 49px;
          margin-left: 10px;
        }
        > img {
          width: 49px;
          height: 49px;
        }
      }
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
.list-file {
    padding-inline-start: 0;
    list-style-type: none;
    width: 1200px;
    margin: 24px auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    li {
        width: 48%;
        display: inline-block;
        background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 4px 24px 4px 24px;
        margin-bottom: 24px;

        img {
            display: inline-block;
            width: 72px;
            height: 72px;
        }

        p {
            display: inline-block;
            font-weight: 500;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
        }

        .left_box {
            display: flex;
            padding-top: 19px;
        }
    }

    .li_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    li:nth-of-type(odd) {
        margin-right: 24px;
    }

    .fileText {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}
</style>
